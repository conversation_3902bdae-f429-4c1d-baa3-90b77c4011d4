"""
Configuration Settings for MELK Chemo Copilot
"""

# Application Information
APP_TITLE = "MELK CHEMO COPILOT"
APP_VERSION = "2.0.0"
APP_DESCRIPTION = "Advanced Chemometric Analysis Tool"

# Branding Configuration
BRANDING_CONFIG = {
    'app_title': APP_TITLE,
    'app_subtitle': APP_DESCRIPTION,
    'version': APP_VERSION,
    'logo': {
        'type': 'spectroscopy',  # Options: 'spectroscopy', 'chemistry', 'custom'
        'text': 'UV-Vis',
        'width': 80,
        'height': 60,
        'mobile_width': 50,
        'mobile_height': 35
    },
    'feature_tags': [
        "🔬 PLS Regression",
        "📊 Spectroscopy",
        "📈 Data Analysis",
        "⚗️ Chemometrics"
    ],
    'page_icon': "🔬",  # Spectroscopy icon instead of DNA
    'favicon_path': None,  # Path to custom favicon if needed
    'custom_logo_path': None  # Path to custom logo if needed
}

# Font Configuration System
FONT_CONFIG = {
    'primary_font': "Nunito Sans",
    'code_font': "Fira Code",
    'fallback_fonts': ["-apple-system", "BlinkMacSystemFont", "'Segoe UI'", "sans-serif"],
    'weights': {
        'light': 300,
        'regular': 400,
        'medium': 500,
        'semibold': 600,
        'bold': 700,
        'extrabold': 800,
        'black': 900
    }
}

# Font Sizes with Responsive Scaling
FONT_SIZES = {
    'app_title': {
        'desktop': "5.5rem",
        'tablet': "4rem",
        'mobile': "3rem"
    },
    'app_subtitle': {
        'desktop': "2.2rem",
        'tablet': "1.8rem",
        'mobile': "1.4rem"
    },
    'step_title': {
        'desktop': "2rem",
        'tablet': "1.6rem",
        'mobile': "1.3rem"
    },
    'button': {
        'desktop': "1.1rem",
        'tablet': "1rem",
        'mobile': "0.9rem"
    },
    'body': {
        'desktop': "1rem",
        'tablet': "0.95rem",
        'mobile': "0.9rem"
    }
}

# Legacy font settings for backward compatibility
FONT_FAMILY = FONT_CONFIG['primary_font']
APP_TITLE_FONT = FONT_CONFIG['primary_font']
APP_TITLE_SIZE = FONT_SIZES['app_title']['desktop']
APP_SUBTITLE_SIZE = FONT_SIZES['app_subtitle']['desktop']
STEP_TITLE_SIZE = FONT_SIZES['step_title']['desktop']
BUTTON_FONT_SIZE = FONT_SIZES['button']['desktop']

# Blue Gradient Color Scheme
UI_COLORS = {
    'primary': '#1E88E5',      # Blue
    'secondary': '#42A5F5',    # Light Blue
    'accent': '#0D47A1',       # Dark Blue
    'warning': '#FF6347',      # Tomato
    'info': '#2196F3',         # Material Blue
    'success': '#4CAF50',      # Green
    'background': '#ffffff',
    'sidebar': '#e3f2fd',      # Very Light Blue
    'text': '#2F4F4F',         # Dark Slate Gray
    'border': '#90CAF9'        # Light Blue
}

# Navigation Colors - Blue Theme
NAV_COLORS = {
    'current': '#1E88E5',      # Blue
    'completed': '#42A5F5',    # Light Blue
    'available': '#64B5F6',    # Medium Light Blue
    'locked': '#A9A9A9',       # Dark Gray
    'hover': '#1565C0'         # Medium Blue
}

# Workflow Steps
WORKFLOW_STEPS = {
    1: "Data Upload",
    2: "Data Overview",
    3: "Preprocessing",
    4: "Variable Selection",
    5: "Model Selection & Cross-Validation",
    6: "Build Model",
    7: "Model Prediction",
    8: "Developed Model Report"
}

# Upload Settings
UPLOAD_SETTINGS = {
    'max_file_size': 200,  # MB
    'accepted_formats': ['.csv', '.xlsx', '.xls'],
    'encoding': 'utf-8'
}

# Plot Settings
PLOT_SETTINGS = {
    'figure_size': (10, 6),
    'dpi': 100,
    'line_width': 1.5,
    'grid_alpha': 0.3,
    'color_palette': ['#0076A8', '#7AC142', '#EDB120', '#D95319', '#4DBEEE']
}

# Preprocessing Methods
PREPROCESSING_METHODS = {
    "mean_centering": {
        "name": "Mean-centering",
        "description": "Centers data by subtracting the mean spectrum from each sample"
    },
    "autoscaling": {
        "name": "Autoscaling",
        "description": "Standardizes variables to unit variance (mean-centering + scaling)"
    },
    "snv": {
        "name": "SNV",
        "description": "Standard Normal Variate - corrects for scatter effects"
    },
    "savgol": {
        "name": "Savitzky-Golay",
        "description": "Polynomial smoothing filter to reduce noise"
    },
    "snv_savgol": {
        "name": "SNV + Savitzky-Golay",
        "description": "Combined SNV and Savitzky-Golay preprocessing"
    }
}

# PLS Algorithms
PLS_ALGORITHMS = {
    "Standard PLS": {
        "description": "Standard Partial Least Squares regression",
        "complexity": "Low",
        "speed": "Fast"
    },
    "NIPALS": {
        "description": "Non-linear Iterative Partial Least Squares",
        "complexity": "Medium",
        "speed": "Medium"
    },
    "SIMPLS": {
        "description": "Statistically Inspired Modification of PLS",
        "complexity": "Medium",
        "speed": "Medium"
    },
    "Kernel PLS": {
        "description": "Kernel-based PLS for non-linear relationships",
        "complexity": "High",
        "speed": "Slow"
    }
}

# Cross-Validation Methods
CV_METHODS = {
    "K-Fold": {
        "description": "K-fold cross-validation with random splitting",
        "parameters": ["n_splits", "shuffle", "random_state"],
        "default_params": {"n_splits": 5, "shuffle": True, "random_state": 42},
        "suitable_for": "General purpose, balanced datasets",
        "pros": ["Simple", "Computationally efficient", "Good for most datasets"],
        "cons": ["May not preserve class distribution", "Variance in results"],
        "recommended_for": ["Medium to large datasets", "General regression problems"]
    },
    "Stratified K-Fold": {
        "description": "K-fold cross-validation preserving target distribution",
        "parameters": ["n_splits", "shuffle", "random_state"],
        "default_params": {"n_splits": 5, "shuffle": True, "random_state": 42},
        "suitable_for": "Classification or imbalanced regression datasets",
        "pros": ["Preserves target distribution", "More stable results", "Better for imbalanced data"],
        "cons": ["Slightly more complex", "Requires continuous stratification for regression"],
        "recommended_for": ["Imbalanced datasets", "Classification problems", "Skewed target distributions"]
    },
    "Leave-One-Out": {
        "description": "Leave-one-out cross-validation (LOOCV) - uses n-1 samples for training",
        "parameters": [],
        "default_params": {},
        "suitable_for": "Small datasets (< 100 samples), maximum data utilization",
        "pros": ["Uses maximum training data", "Deterministic results", "No randomness"],
        "cons": ["Computationally expensive", "High variance", "Not suitable for large datasets"],
        "recommended_for": ["Very small datasets", "When every sample is precious", "Deterministic validation"]
    },
    "Leave-P-Out": {
        "description": "Leave-P-out cross-validation - systematically leaves out P samples",
        "parameters": ["p"],
        "default_params": {"p": 2},
        "suitable_for": "Small datasets, specific validation requirements",
        "pros": ["Flexible validation size", "Thorough testing", "Systematic approach"],
        "cons": ["Computationally very expensive", "Combinatorial explosion", "Impractical for large P"],
        "recommended_for": ["Small datasets", "When specific validation size is needed", "Research validation"]
    },
    "Repeated K-Fold": {
        "description": "Repeated K-fold cross-validation for more robust performance estimates",
        "parameters": ["n_splits", "n_repeats", "random_state"],
        "default_params": {"n_splits": 5, "n_repeats": 3, "random_state": 42},
        "suitable_for": "When robust performance estimates are critical",
        "pros": ["More robust estimates", "Reduces variance", "Better confidence intervals", "Statistical significance"],
        "cons": ["Computationally expensive", "Longer training time", "Multiple random seeds"],
        "recommended_for": ["Model comparison", "Publication-quality results", "Statistical analysis"]
    },
    "Shuffle Split": {
        "description": "Random permutation cross-validation with flexible train/test ratios",
        "parameters": ["n_splits", "test_size", "train_size", "random_state"],
        "default_params": {"n_splits": 10, "test_size": 0.2, "train_size": None, "random_state": 42},
        "suitable_for": "Large datasets, flexible train/test ratios",
        "pros": ["Flexible test size", "Independent splits", "Good for large datasets", "Customizable ratios"],
        "cons": ["May have overlapping test sets", "Less systematic than K-Fold", "Potential data leakage"],
        "recommended_for": ["Large datasets", "When specific train/test ratios are needed", "Quick validation"]
    },
    "Time Series Split": {
        "description": "Time series cross-validation respecting temporal ordering",
        "parameters": ["n_splits", "max_train_size", "test_size", "gap"],
        "default_params": {"n_splits": 5, "max_train_size": None, "test_size": None, "gap": 0},
        "suitable_for": "Time series data, temporal dependencies",
        "pros": ["Respects temporal order", "Realistic for time series", "Prevents future leakage"],
        "cons": ["Only for time series", "Requires ordered data", "Unequal training sizes"],
        "recommended_for": ["Time series forecasting", "Sequential data", "Temporal analysis"]
    },
    "Group K-Fold": {
        "description": "K-fold cross-validation with group constraints to prevent data leakage",
        "parameters": ["n_splits"],
        "default_params": {"n_splits": 5},
        "suitable_for": "Data with natural groupings (e.g., multiple samples from same source)",
        "pros": ["Prevents data leakage", "Realistic validation", "Respects data structure"],
        "cons": ["Requires group information", "May have unbalanced folds", "Complex setup"],
        "recommended_for": ["Grouped data", "Multiple measurements per subject", "Hierarchical data"]
    },
    "Monte Carlo Cross-Validation": {
        "description": "Random subsampling cross-validation with multiple random splits",
        "parameters": ["n_splits", "test_size", "random_state"],
        "default_params": {"n_splits": 100, "test_size": 0.2, "random_state": 42},
        "suitable_for": "Large datasets, when many random splits are beneficial",
        "pros": ["Many independent estimates", "Smooth performance curves", "Good statistical properties"],
        "cons": ["Computationally intensive", "Overlapping test sets", "May overfit to validation"],
        "recommended_for": ["Large datasets", "Smooth performance estimation", "Research applications"]
    },
    "Nested Cross-Validation": {
        "description": "Two-level cross-validation for unbiased model selection and evaluation",
        "parameters": ["outer_cv", "inner_cv"],
        "default_params": {"outer_cv": 5, "inner_cv": 3},
        "suitable_for": "Model selection with unbiased performance estimation",
        "pros": ["Unbiased performance estimates", "Proper model selection", "Publication quality"],
        "cons": ["Very computationally expensive", "Complex interpretation", "Long training time"],
        "recommended_for": ["Model comparison studies", "Hyperparameter optimization", "Research validation"]
    }
}

# Default Parameters
DEFAULT_PARAMS = {
    'max_latent_variables': 15,
    'cv_folds': 10,
    'random_state': 42,
    'savgol_window': 11,
    'savgol_polyorder': 2
}

# Variable Selection Methods
VARIABLE_SELECTION_METHODS = {
    "VIP": {
        "name": "Variable Importance in Projection",
        "description": "Select variables based on VIP scores"
    },
    "Genetic Algorithm": {
        "name": "Genetic Algorithm",
        "description": "Evolutionary optimization for variable selection"
    },
    "Interval PLS": {
        "name": "Interval PLS",
        "description": "Select optimal spectral intervals"
    },
    "CARS": {
        "name": "CARS",
        "description": "Competitive Adaptive Reweighted Sampling"
    }
}
