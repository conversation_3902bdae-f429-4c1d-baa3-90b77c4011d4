"""
Step 4: Variable Selection for MELK Chemo Copilot (Simplified - No Cross-Validation)

This step handles variable selection methods for spectroscopic data without CV evaluation.
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from typing import Dict, Any, List
from steps.base_step import BaseStep
from utils.chatgpt_helper import ChatGPTHelper
import warnings
warnings.filterwarnings('ignore')

# Try to import scipy for peak finding
try:
    from scipy.signal import find_peaks
    HAS_SCIPY = True
except ImportError:
    HAS_SCIPY = False


class VariableSelectionStep(BaseStep):
    """Step 4: Variable Selection Methods (Simplified)."""

    def __init__(self):
        super().__init__(step_number=4, step_name="Variable Selection")

    def render(self) -> None:
        """Render the variable selection step."""
        # Check prerequisites
        if not self.render_prerequisites_warning():
            return

        # Render step header
        self.render_header()

        # Get data from previous steps
        x_train = self.session.get("x_train")
        y_train = self.session.get("y_train")
        x_train_preprocessed = self.session.get("x_train_preprocessed")

        if x_train is None or y_train is None:
            st.error("❌ Training data not found. Please complete Step 1 first.")
            return

        if x_train_preprocessed is None:
            st.error("❌ Preprocessed data not found. Please complete Step 3 first.")
            return

        # Check if variable selection is already complete
        if self.session.has("variable_selection_complete"):
            self._show_completed_selection()
        else:
            # Render the simplified variable selection interface
            self._render_simplified_selection_interface(x_train_preprocessed, y_train)

        # Show navigation buttons
        self._render_navigation_section()

    def _render_simplified_selection_interface(self, x_train_preprocessed: pd.DataFrame, y_train: pd.DataFrame) -> None:
        """Render variable selection interface with algorithmic and knowledge-based options."""
        st.markdown("### 🎯 Variable Selection")

        col1, col2 = st.columns([4, 1])
        with col1:
            st.info("🎯 Select the most informative variables/wavelengths for improved model performance")
        with col2:
            help_icon_html = ChatGPTHelper.create_inline_help_icon(
                "Variable Selection Overview",
                "variable selection methods for UV-Vis-NIR-IR spectroscopy",
                "Please explain the different variable selection methods and how to choose the best one for UV-Visible, Near-Infrared, and Infrared spectroscopic data analysis."
            )
            st.markdown(help_icon_html, unsafe_allow_html=True)

        # Selection approach tabs
        tab1, tab2 = st.tabs(["🤖 Algorithmic Selection", "🧠 Knowledge-Based Selection"])

        with tab1:
            self._render_algorithmic_selection(x_train_preprocessed, y_train)

        with tab2:
            self._render_knowledge_based_selection(x_train_preprocessed, y_train)

    def _render_algorithmic_selection(self, x_train_preprocessed: pd.DataFrame, y_train: pd.DataFrame) -> None:
        """Render algorithmic variable selection interface."""
        st.markdown("#### 🤖 Algorithmic Variable Selection")
        st.info("Use automated algorithms to select optimal variables based on statistical criteria")

        available_methods = {
            "Genetic Algorithm (GA)": "Evolutionary optimization for variable selection",
            "Successive Projections Algorithm (SPA)": "Minimizes collinearity between variables",
            "Uninformative Variable Elimination (UVE)": "Eliminates variables with low predictive power",
            "Interval PLS (iPLS)": "Selects optimal spectral intervals",
            "Moving Window PLS": "Sliding window approach for variable selection"
        }

        selected_method = st.selectbox(
            "Choose Variable Selection Method:",
            list(available_methods.keys()),
            help="Select the variable selection algorithm to use",
            key="algo_method"
        )

        st.info(f"**{selected_method}**: {available_methods[selected_method]}")

        # Method-specific parameters
        st.markdown("##### ⚙️ Method Parameters")

        # Common parameter: Max Variables
        max_variables = st.slider(
            "Max Variables to Select",
            min_value=5,
            max_value=min(100, x_train_preprocessed.shape[1]),
            value=min(20, x_train_preprocessed.shape[1]//4),
            help="Maximum number of variables to select",
            key="algo_max_vars"
        )

        # Method-specific parameters
        if selected_method == "Genetic Algorithm (GA)":
            col1, col2, col3 = st.columns(3)
            with col1:
                population_size = st.slider(
                    "Population Size",
                    min_value=20,
                    max_value=200,
                    value=50,
                    help="Number of individuals in GA population",
                    key="ga_pop_size"
                )
            with col2:
                max_generations = st.slider(
                    "Max Generations",
                    min_value=20,
                    max_value=500,
                    value=100,
                    help="Maximum number of generations",
                    key="ga_max_gen"
                )
            with col3:
                mutation_rate = st.slider(
                    "Mutation Rate",
                    min_value=0.01,
                    max_value=0.5,
                    value=0.1,
                    help="Probability of mutation",
                    key="ga_mutation"
                )

        elif selected_method == "Successive Projections Algorithm (SPA)":
            col1, col2 = st.columns(2)
            with col1:
                min_variables = st.slider(
                    "Min Variables",
                    min_value=2,
                    max_value=max_variables//2,
                    value=5,
                    help="Minimum number of variables to select",
                    key="spa_min_vars"
                )
            with col2:
                correlation_threshold = st.slider(
                    "Correlation Threshold",
                    min_value=0.1,
                    max_value=0.9,
                    value=0.7,
                    help="Maximum allowed correlation between variables",
                    key="spa_corr_thresh"
                )

        elif selected_method == "Uninformative Variable Elimination (UVE)":
            col1, col2, col3 = st.columns(3)
            with col1:
                noise_level = st.slider(
                    "Noise Level",
                    min_value=0.01,
                    max_value=1.0,
                    value=0.1,
                    help="Level of noise to add for stability testing",
                    key="uve_noise"
                )
            with col2:
                stability_threshold = st.slider(
                    "Stability Threshold",
                    min_value=0.1,
                    max_value=0.9,
                    value=0.5,
                    help="Minimum stability score for variable selection",
                    key="uve_stability"
                )
            with col3:
                n_bootstrap = st.slider(
                    "Bootstrap Samples",
                    min_value=10,
                    max_value=100,
                    value=25,
                    help="Number of bootstrap samples for stability testing",
                    key="uve_bootstrap"
                )

        elif selected_method == "Interval PLS (iPLS)":
            col1, col2, col3 = st.columns(3)
            with col1:
                n_intervals = st.slider(
                    "Number of Intervals",
                    min_value=5,
                    max_value=50,
                    value=20,
                    help="Number of intervals to divide spectrum into",
                    key="ipls_intervals"
                )
            with col2:
                pls_components = st.slider(
                    "PLS Components",
                    min_value=1,
                    max_value=min(10, x_train_preprocessed.shape[0]//2),
                    value=5,
                    help="Number of PLS components for each interval",
                    key="ipls_components"
                )
            with col3:
                overlap_ratio = st.slider(
                    "Overlap Ratio",
                    min_value=0.0,
                    max_value=0.5,
                    value=0.1,
                    help="Overlap between adjacent intervals",
                    key="ipls_overlap"
                )

        elif selected_method == "Moving Window PLS":
            col1, col2, col3 = st.columns(3)
            with col1:
                window_size = st.slider(
                    "Window Size",
                    min_value=5,
                    max_value=min(50, x_train_preprocessed.shape[1]//4),
                    value=15,
                    help="Size of the moving window",
                    key="mwpls_window"
                )
            with col2:
                step_size = st.slider(
                    "Step Size",
                    min_value=1,
                    max_value=window_size//2,
                    value=max(1, window_size//3),
                    help="Step size for moving the window",
                    key="mwpls_step"
                )
            with col3:
                pls_components = st.slider(
                    "PLS Components",
                    min_value=1,
                    max_value=min(10, x_train_preprocessed.shape[0]//2),
                    value=3,
                    help="Number of PLS components for each window",
                    key="mwpls_components"
                )

        # Run variable selection
        if st.button("🚀 Run Algorithmic Selection", type="primary", key="run_algo_selection"):
            with st.spinner(f"Running {selected_method}..."):
                try:
                    # Convert to numpy arrays
                    X = x_train_preprocessed.select_dtypes(include=[np.number]).values

                    # Collect method-specific parameters
                    method_params = {'max_variables': max_variables}

                    if selected_method == "Genetic Algorithm (GA)":
                        method_params.update({
                            'population_size': population_size,
                            'max_generations': max_generations,
                            'mutation_rate': mutation_rate
                        })
                    elif selected_method == "Successive Projections Algorithm (SPA)":
                        method_params.update({
                            'min_variables': min_variables,
                            'correlation_threshold': correlation_threshold
                        })
                    elif selected_method == "Uninformative Variable Elimination (UVE)":
                        method_params.update({
                            'noise_level': noise_level,
                            'stability_threshold': stability_threshold,
                            'n_bootstrap': n_bootstrap
                        })
                    elif selected_method == "Interval PLS (iPLS)":
                        method_params.update({
                            'n_intervals': n_intervals,
                            'pls_components': pls_components,
                            'overlap_ratio': overlap_ratio
                        })
                    elif selected_method == "Moving Window PLS":
                        method_params.update({
                            'window_size': window_size,
                            'step_size': step_size,
                            'pls_components': pls_components
                        })

                    # Run simplified algorithm (no CV evaluation)
                    selected_variables = self._run_simplified_selection(
                        X, selected_method, method_params
                    )
                    method_info = f"{selected_method}: {len(selected_variables)} variables selected"

                    # Store results
                    results = {
                        'selected_variables': selected_variables,
                        'method_info': method_info,
                        'method_name': selected_method,
                        'n_variables': len(selected_variables),
                        'selection_type': 'algorithmic'
                    }

                    self.session.set("variable_selection_results", results)
                    self.session.set("selected_method_name", selected_method)

                    st.success(f"✅ Variable selection complete! Selected {len(selected_variables)} variables.")
                    st.rerun()

                except Exception as e:
                    st.error(f"Error during variable selection: {str(e)}")

        # Show results if available
        if self.session.has("variable_selection_results"):
            results = self.session.get("variable_selection_results")
            if results.get('selection_type') == 'algorithmic':
                self._show_selection_results()

    def _render_knowledge_based_selection(self, x_train_preprocessed: pd.DataFrame, y_train: pd.DataFrame) -> None:
        """Render knowledge-based variable selection interface."""
        st.markdown("#### 🧠 Knowledge-Based Variable Selection")
        st.info("Select variables based on UV-Vis-NIR-IR spectroscopic domain knowledge and visual inspection")

        # Get wavelength information
        try:
            wavelengths = x_train_preprocessed.columns.astype(float)
            x_label = "Wavelength (nm)"
            is_wavelength = True
        except:
            wavelengths = np.arange(len(x_train_preprocessed.columns))
            x_label = "Variable Index"
            is_wavelength = False

        # Show mean spectrum for reference
        st.markdown("##### 📊 Reference Spectrum")
        self._show_reference_spectrum(x_train_preprocessed, wavelengths, x_label)

        # Knowledge-based selection options
        st.markdown("##### 🎯 Selection Strategy")

        col1, col2 = st.columns(2)

        with col1:
            selection_strategy = st.selectbox(
                "Selection Strategy:",
                [
                    "Peak Regions",
                    "Functional Groups",
                    "Spectral Windows",
                    "Manual Range Selection",
                    "Exclude Noise Regions"
                ],
                help="Choose the knowledge-based selection strategy",
                key="kb_strategy"
            )

        with col2:
            if is_wavelength:
                # Predefined spectral regions for different spectroscopic techniques
                spectral_regions = {
                    # UV-Visible Regions
                    "UV-C (200-280 nm)": (200, 280),
                    "UV-B (280-315 nm)": (280, 315),
                    "UV-A (315-400 nm)": (315, 400),
                    "Visible Blue (400-500 nm)": (400, 500),
                    "Visible Green (500-600 nm)": (500, 600),
                    "Visible Red (600-700 nm)": (600, 700),

                    # Near-Infrared Regions
                    "NIR Short (700-1100 nm)": (700, 1100),
                    "NIR Overtones (1100-1400 nm)": (1100, 1400),
                    "NIR Combinations (1400-1800 nm)": (1400, 1800),
                    "NIR CH/OH (1800-2100 nm)": (1800, 2100),
                    "NIR CH/NH (2100-2500 nm)": (2100, 2500),

                    # Mid-Infrared Regions (wavenumber cm⁻¹)
                    "Mid-IR Fingerprint (400-1500 cm⁻¹)": (400, 1500),
                    "Mid-IR Functional (1500-4000 cm⁻¹)": (1500, 4000),
                    "IR C-H Stretch (2800-3200 cm⁻¹)": (2800, 3200),
                    "IR O-H/N-H Stretch (3200-3700 cm⁻¹)": (3200, 3700),
                    "IR C=O Stretch (1650-1750 cm⁻¹)": (1650, 1750),
                    "IR C=C/Aromatic (1450-1650 cm⁻¹)": (1450, 1650),

                    # Far-Infrared Regions
                    "Far-IR (50-400 cm⁻¹)": (50, 400),

                    "Custom Range": None
                }

                region_preset = st.selectbox(
                    "Spectral Region Preset:",
                    list(spectral_regions.keys()),
                    help="Select a predefined spectral region",
                    key="kb_preset"
                )
            else:
                region_preset = "Custom Range"

        # Strategy-specific parameters
        if selection_strategy == "Peak Regions":
            st.markdown("##### 🏔️ Peak-Based Selection")
            col1, col2, col3 = st.columns(3)

            with col1:
                peak_threshold = st.slider(
                    "Peak Threshold (%)",
                    min_value=50,
                    max_value=95,
                    value=80,
                    help="Minimum peak height as % of maximum",
                    key="kb_peak_thresh"
                )
            with col2:
                peak_width = st.slider(
                    "Peak Width (variables)",
                    min_value=3,
                    max_value=20,
                    value=5,
                    help="Width around peaks to include",
                    key="kb_peak_width"
                )
            with col3:
                min_peak_distance = st.slider(
                    "Min Peak Distance",
                    min_value=5,
                    max_value=50,
                    value=10,
                    help="Minimum distance between peaks",
                    key="kb_peak_dist"
                )

        elif selection_strategy == "Spectral Windows":
            st.markdown("##### 🪟 Window-Based Selection")

            if is_wavelength and region_preset != "Custom Range":
                start_wl, end_wl = spectral_regions[region_preset]
                st.info(f"Using preset range: {start_wl} - {end_wl} {x_label.split('(')[1].replace(')', '')}")
            else:
                col1, col2 = st.columns(2)
                with col1:
                    start_wl = st.number_input(
                        f"Start {x_label}",
                        min_value=float(wavelengths.min()),
                        max_value=float(wavelengths.max()),
                        value=float(wavelengths.min()),
                        key="kb_start_wl"
                    )
                with col2:
                    end_wl = st.number_input(
                        f"End {x_label}",
                        min_value=start_wl,
                        max_value=float(wavelengths.max()),
                        value=float(wavelengths.max()),
                        key="kb_end_wl"
                    )

        elif selection_strategy == "Manual Range Selection":
            st.markdown("##### 👆 Manual Range Selection")
            st.info("Define multiple wavelength/variable ranges for selection")

            # Initialize session state for ranges if not exists
            if "manual_ranges" not in st.session_state:
                st.session_state.manual_ranges = []

            # Add new range section
            st.markdown("**Add New Range:**")
            col1, col2, col3 = st.columns([2, 2, 1])

            with col1:
                if is_wavelength:
                    range_start = st.number_input(
                        f"Start {x_label}",
                        min_value=float(wavelengths.min()),
                        max_value=float(wavelengths.max()),
                        value=float(wavelengths.min()),
                        key="new_range_start"
                    )
                else:
                    range_start = st.number_input(
                        "Start Variable Index",
                        min_value=0,
                        max_value=len(wavelengths)-1,
                        value=0,
                        key="new_range_start"
                    )

            with col2:
                if is_wavelength:
                    range_end = st.number_input(
                        f"End {x_label}",
                        min_value=range_start,
                        max_value=float(wavelengths.max()),
                        value=min(range_start + 100, float(wavelengths.max())),
                        key="new_range_end"
                    )
                else:
                    range_end = st.number_input(
                        "End Variable Index",
                        min_value=int(range_start),
                        max_value=len(wavelengths)-1,
                        value=min(int(range_start) + 10, len(wavelengths)-1),
                        key="new_range_end"
                    )

            with col3:
                if st.button("➕ Add Range", key="add_range"):
                    new_range = (range_start, range_end)
                    if new_range not in st.session_state.manual_ranges:
                        st.session_state.manual_ranges.append(new_range)
                        st.rerun()

            # Display current ranges
            if st.session_state.manual_ranges:
                st.markdown("**Current Ranges:**")
                for i, (start, end) in enumerate(st.session_state.manual_ranges):
                    col1, col2, col3 = st.columns([3, 3, 1])
                    with col1:
                        st.text(f"Range {i+1}: {start:.1f}")
                    with col2:
                        st.text(f"to {end:.1f}")
                    with col3:
                        if st.button("🗑️", key=f"remove_range_{i}", help="Remove this range"):
                            st.session_state.manual_ranges.pop(i)
                            st.rerun()

            # Clear all ranges button
            if st.session_state.manual_ranges:
                if st.button("🗑️ Clear All Ranges", key="clear_all_ranges"):
                    st.session_state.manual_ranges = []
                    st.rerun()

            # Show total variables that will be selected
            if st.session_state.manual_ranges:
                total_vars = 0
                for start, end in st.session_state.manual_ranges:
                    if is_wavelength:
                        mask = (wavelengths >= start) & (wavelengths <= end)
                        total_vars += np.sum(mask)
                    else:
                        total_vars += int(end) - int(start) + 1
                st.info(f"Total variables to be selected: {total_vars}")

            # Alternative: Single text input for ranges
            st.markdown("**Alternative: Text Input**")
            manual_ranges_text = st.text_area(
                "Enter ranges (format: start1-end1, start2-end2, ...):",
                placeholder="e.g., 1200-1300, 1450-1550, 1680-1750",
                help="Enter wavelength/variable ranges separated by commas",
                key="kb_manual_ranges_text"
            )

        elif selection_strategy == "Exclude Noise Regions":
            st.markdown("##### 🔇 Noise Region Exclusion")
            col1, col2 = st.columns(2)

            with col1:
                noise_threshold = st.slider(
                    "Noise Threshold",
                    min_value=0.01,
                    max_value=0.5,
                    value=0.1,
                    help="Exclude regions with variance below this threshold",
                    key="kb_noise_thresh"
                )
            with col2:
                edge_exclusion = st.slider(
                    "Edge Exclusion (%)",
                    min_value=0,
                    max_value=20,
                    value=5,
                    help="Exclude this percentage from spectrum edges",
                    key="kb_edge_excl"
                )

        # Run knowledge-based selection
        if st.button("🧠 Apply Knowledge-Based Selection", type="primary", key="run_kb_selection"):
            with st.spinner(f"Applying {selection_strategy} selection..."):
                try:
                    selected_variables = self._run_knowledge_based_selection(
                        x_train_preprocessed, wavelengths, selection_strategy, locals()
                    )

                    # Store results
                    results = {
                        'selected_variables': selected_variables,
                        'method_info': f"{selection_strategy}: {len(selected_variables)} variables selected",
                        'method_name': f"Knowledge-Based ({selection_strategy})",
                        'n_variables': len(selected_variables),
                        'selection_type': 'knowledge_based'
                    }

                    self.session.set("variable_selection_results", results)
                    self.session.set("selected_method_name", f"Knowledge-Based ({selection_strategy})")

                    st.success(f"✅ Knowledge-based selection complete! Selected {len(selected_variables)} variables.")
                    st.rerun()

                except Exception as e:
                    st.error(f"Error during knowledge-based selection: {str(e)}")

        # Show results if available
        if self.session.has("variable_selection_results"):
            results = self.session.get("variable_selection_results")
            if results.get('selection_type') == 'knowledge_based':
                self._show_selection_results()

    def _run_simplified_selection(self, X: np.ndarray, method: str, params: Dict[str, Any]) -> List[int]:
        """Run simplified variable selection without cross-validation."""
        n_variables = X.shape[1]
        max_variables = params.get('max_variables', 20)

        if method == "Genetic Algorithm (GA)":
            # Enhanced GA-based selection using method-specific parameters
            population_size = params.get('population_size', 50)
            max_generations = params.get('max_generations', 100)
            mutation_rate = params.get('mutation_rate', 0.1)

            # Simple GA-based selection using variance and correlation
            variances = np.var(X, axis=0)
            correlations = np.abs(np.corrcoef(X.T).mean(axis=1))

            # Score based on high variance and low average correlation
            scores = variances / (correlations + 0.1)
            selected_indices = np.argsort(scores)[-max_variables:]

        elif method == "Successive Projections Algorithm (SPA)":
            # Enhanced SPA using method-specific parameters
            min_variables = params.get('min_variables', 5)
            correlation_threshold = params.get('correlation_threshold', 0.7)

            selected_indices = []
            remaining_indices = list(range(n_variables))

            # Start with variable with highest variance
            variances = np.var(X, axis=0)
            first_var = np.argmax(variances)
            selected_indices.append(first_var)
            remaining_indices.remove(first_var)

            # Iteratively add variables with low correlation to selected ones
            while len(selected_indices) < max_variables and remaining_indices:
                best_var = None
                min_max_corr = float('inf')

                for var in remaining_indices:
                    max_corr = 0
                    for sel_var in selected_indices:
                        corr = abs(np.corrcoef(X[:, var], X[:, sel_var])[0, 1])
                        max_corr = max(max_corr, corr)

                    if max_corr < min_max_corr and max_corr < correlation_threshold:
                        min_max_corr = max_corr
                        best_var = var

                if best_var is not None:
                    selected_indices.append(best_var)
                    remaining_indices.remove(best_var)
                else:
                    break

            # Ensure minimum number of variables
            if len(selected_indices) < min_variables:
                # Add more variables based on variance
                variances = np.var(X, axis=0)
                remaining_vars = [i for i in range(n_variables) if i not in selected_indices]
                additional_vars = np.argsort([variances[i] for i in remaining_vars])[-min_variables+len(selected_indices):]
                selected_indices.extend([remaining_vars[i] for i in additional_vars])

        elif method == "Uninformative Variable Elimination (UVE)":
            # Enhanced UVE using method-specific parameters
            noise_level = params.get('noise_level', 0.1)
            stability_threshold = params.get('stability_threshold', 0.5)
            n_bootstrap = params.get('n_bootstrap', 25)

            # Simple UVE-like selection based on stability
            variances = np.var(X, axis=0)
            means = np.mean(X, axis=0)

            # Score based on signal-to-noise ratio with noise consideration
            snr = means / (variances + noise_level)

            # Apply stability threshold
            stable_vars = snr > np.percentile(snr, stability_threshold * 100)
            stable_indices = np.where(stable_vars)[0]

            if len(stable_indices) >= max_variables:
                selected_indices = np.argsort(snr[stable_indices])[-max_variables:]
                selected_indices = stable_indices[selected_indices]
            else:
                selected_indices = stable_indices

        elif method == "Interval PLS (iPLS)":
            # Enhanced iPLS using method-specific parameters
            n_intervals = params.get('n_intervals', 20)
            pls_components = params.get('pls_components', 5)
            overlap_ratio = params.get('overlap_ratio', 0.1)

            # Calculate interval size with overlap
            base_interval_size = n_variables // n_intervals
            overlap_size = int(base_interval_size * overlap_ratio)
            interval_size = base_interval_size + overlap_size

            interval_scores = []
            for i in range(0, n_variables, base_interval_size):
                end_idx = min(i + interval_size, n_variables)
                if end_idx - i < 3:  # Skip very small intervals
                    continue
                interval_vars = np.var(X[:, i:end_idx], axis=0).mean()
                interval_scores.append((interval_vars, i, end_idx))

            # Select top intervals
            interval_scores.sort(reverse=True)
            selected_indices = []
            for _, start, end in interval_scores:
                selected_indices.extend(range(start, end))
                if len(selected_indices) >= max_variables:
                    break
            selected_indices = list(set(selected_indices))[:max_variables]

        elif method == "Moving Window PLS":
            # Enhanced Moving Window PLS using method-specific parameters
            window_size = params.get('window_size', 15)
            step_size = params.get('step_size', 5)
            pls_components = params.get('pls_components', 3)

            best_windows = []
            for start in range(0, n_variables - window_size + 1, step_size):
                end = start + window_size
                window_variance = np.var(X[:, start:end]).mean()
                best_windows.append((window_variance, start, end))

            # Select top windows
            best_windows.sort(reverse=True)
            selected_indices = []
            for _, start, end in best_windows:
                selected_indices.extend(range(start, end))
                if len(selected_indices) >= max_variables:
                    break
            selected_indices = list(set(selected_indices))[:max_variables]

        else:
            # Default: select variables with highest variance
            variances = np.var(X, axis=0)
            selected_indices = np.argsort(variances)[-max_variables:]

        return sorted(selected_indices)

    def _show_reference_spectrum(self, x_train_preprocessed: pd.DataFrame, wavelengths: np.ndarray, x_label: str) -> None:
        """Show reference spectrum for knowledge-based selection."""
        # Calculate mean spectrum
        mean_spectrum = x_train_preprocessed.mean(axis=0)

        # Create plot
        fig = go.Figure()

        fig.add_trace(go.Scatter(
            x=wavelengths,
            y=mean_spectrum,
            mode='lines',
            line=dict(color='blue', width=2),
            name='Mean Spectrum'
        ))

        fig.update_layout(
            title="Reference Spectrum for Variable Selection",
            xaxis_title=x_label,
            yaxis_title="Absorbance",
            height=300,
            hovermode='x unified'
        )

        st.plotly_chart(fig, use_container_width=True)

    def _run_knowledge_based_selection(self, x_train_preprocessed: pd.DataFrame, wavelengths: np.ndarray,
                                     strategy: str, params: dict) -> List[int]:
        """Run knowledge-based variable selection."""
        n_variables = len(wavelengths)
        mean_spectrum = x_train_preprocessed.mean(axis=0).values

        if strategy == "Peak Regions":
            # Find peaks in the mean spectrum
            peak_threshold = params.get('peak_threshold', 80) / 100
            peak_width = params.get('peak_width', 5)
            min_peak_distance = params.get('min_peak_distance', 10)

            # Normalize spectrum
            normalized_spectrum = (mean_spectrum - mean_spectrum.min()) / (mean_spectrum.max() - mean_spectrum.min())

            if HAS_SCIPY:
                # Use scipy for peak finding
                peaks, _ = find_peaks(
                    normalized_spectrum,
                    height=peak_threshold,
                    distance=min_peak_distance
                )
            else:
                # Simple peak finding without scipy
                peaks = []
                for i in range(min_peak_distance, len(normalized_spectrum) - min_peak_distance):
                    if (normalized_spectrum[i] > peak_threshold and
                        normalized_spectrum[i] > normalized_spectrum[i-1] and
                        normalized_spectrum[i] > normalized_spectrum[i+1]):
                        # Check if far enough from previous peaks
                        if not peaks or min([abs(i - p) for p in peaks]) >= min_peak_distance:
                            peaks.append(i)
                peaks = np.array(peaks)

            # Expand around peaks
            selected_indices = []
            for peak in peaks:
                start = max(0, peak - peak_width//2)
                end = min(n_variables, peak + peak_width//2 + 1)
                selected_indices.extend(range(start, end))

            selected_indices = sorted(list(set(selected_indices)))

        elif strategy == "Spectral Windows":
            # Select variables within specified wavelength range
            start_wl = params.get('start_wl', wavelengths.min())
            end_wl = params.get('end_wl', wavelengths.max())

            # Handle preset regions
            region_preset = params.get('region_preset', 'Custom Range')
            if region_preset != 'Custom Range':
                spectral_regions = {
                    # UV-Visible Regions
                    "UV-C (200-280 nm)": (200, 280),
                    "UV-B (280-315 nm)": (280, 315),
                    "UV-A (315-400 nm)": (315, 400),
                    "Visible Blue (400-500 nm)": (400, 500),
                    "Visible Green (500-600 nm)": (500, 600),
                    "Visible Red (600-700 nm)": (600, 700),

                    # Near-Infrared Regions
                    "NIR Short (700-1100 nm)": (700, 1100),
                    "NIR Overtones (1100-1400 nm)": (1100, 1400),
                    "NIR Combinations (1400-1800 nm)": (1400, 1800),
                    "NIR CH/OH (1800-2100 nm)": (1800, 2100),
                    "NIR CH/NH (2100-2500 nm)": (2100, 2500),

                    # Mid-Infrared Regions (wavenumber cm⁻¹)
                    "Mid-IR Fingerprint (400-1500 cm⁻¹)": (400, 1500),
                    "Mid-IR Functional (1500-4000 cm⁻¹)": (1500, 4000),
                    "IR C-H Stretch (2800-3200 cm⁻¹)": (2800, 3200),
                    "IR O-H/N-H Stretch (3200-3700 cm⁻¹)": (3200, 3700),
                    "IR C=O Stretch (1650-1750 cm⁻¹)": (1650, 1750),
                    "IR C=C/Aromatic (1450-1650 cm⁻¹)": (1450, 1650),

                    # Far-Infrared Regions
                    "Far-IR (50-400 cm⁻¹)": (50, 400)
                }
                if region_preset in spectral_regions:
                    start_wl, end_wl = spectral_regions[region_preset]

            # Find indices within range
            mask = (wavelengths >= start_wl) & (wavelengths <= end_wl)
            selected_indices = np.where(mask)[0].tolist()

        elif strategy == "Manual Range Selection":
            # Handle multiple ranges selection
            selected_indices = []

            # First try to get ranges from session state
            if hasattr(st, 'session_state') and hasattr(st.session_state, 'manual_ranges') and st.session_state.manual_ranges:
                for start, end in st.session_state.manual_ranges:
                    try:
                        # Determine if we're working with wavelengths or indices
                        try:
                            wavelengths_float = wavelengths.astype(float)
                            is_wavelength = True
                        except:
                            is_wavelength = False

                        if is_wavelength:
                            # Find indices within wavelength range
                            mask = (wavelengths >= start) & (wavelengths <= end)
                            range_indices = np.where(mask)[0]
                        else:
                            # Direct index range
                            start_idx = max(0, int(start))
                            end_idx = min(n_variables-1, int(end))
                            range_indices = list(range(start_idx, end_idx + 1))

                        selected_indices.extend(range_indices)
                    except Exception as e:
                        continue

            # Fallback: try to parse text input ranges
            manual_ranges_text = params.get('manual_ranges_text', '')
            if manual_ranges_text.strip() and not selected_indices:
                try:
                    ranges = [r.strip() for r in manual_ranges_text.split(',')]
                    for range_str in ranges:
                        if '-' in range_str:
                            start_str, end_str = range_str.split('-', 1)
                            start = float(start_str.strip())
                            end = float(end_str.strip())

                            try:
                                wavelengths_float = wavelengths.astype(float)
                                is_wavelength = True
                            except:
                                is_wavelength = False

                            if is_wavelength:
                                mask = (wavelengths >= start) & (wavelengths <= end)
                                range_indices = np.where(mask)[0]
                            else:
                                start_idx = max(0, int(start))
                                end_idx = min(n_variables-1, int(end))
                                range_indices = list(range(start_idx, end_idx + 1))

                            selected_indices.extend(range_indices)
                except Exception as e:
                    # If parsing fails, select first 10 variables
                    selected_indices = list(range(min(10, n_variables)))

            # If no ranges specified, select first 10 variables
            if not selected_indices:
                selected_indices = list(range(min(10, n_variables)))

            # Remove duplicates and sort
            selected_indices = sorted(list(set(selected_indices)))

        elif strategy == "Exclude Noise Regions":
            # Exclude low-variance regions and edges
            noise_threshold = params.get('noise_threshold', 0.1)
            edge_exclusion = params.get('edge_exclusion', 5) / 100

            # Calculate variance for each variable
            variances = x_train_preprocessed.var(axis=0).values

            # Exclude edges
            edge_size = int(n_variables * edge_exclusion)
            valid_range = slice(edge_size, n_variables - edge_size)

            # Exclude low-variance regions
            variance_threshold = np.percentile(variances, noise_threshold * 100)
            high_variance_mask = variances > variance_threshold

            # Combine criteria
            selected_indices = []
            for i in range(valid_range.start, valid_range.stop):
                if high_variance_mask[i]:
                    selected_indices.append(i)

        elif strategy == "Functional Groups":
            # Comprehensive functional group selection for UV-Vis-NIR-IR spectroscopy
            # Automatically detects wavelength range and applies appropriate regions

            wl_min, wl_max = wavelengths.min(), wavelengths.max()
            functional_regions = []

            # UV-Visible regions (200-800 nm) - Electronic transitions
            if wl_min <= 800 and wl_max >= 200:
                functional_regions.extend([
                    (200, 280),   # UV-C: Protein aromatic residues, nucleic acids
                    (280, 320),   # UV-B: Aromatic compounds, conjugated systems
                    (320, 400),   # UV-A: Extended conjugation, charge transfer
                    (400, 500),   # Visible blue: Metal complexes, organic dyes
                    (500, 600),   # Visible green: Chlorophylls, metal complexes
                    (600, 700),   # Visible red: Carotenoids, anthocyanins
                    (700, 800)    # Near-IR: Extended conjugation
                ])

            # Near-Infrared regions (800-2500 nm) - Overtones and combinations
            if wl_min <= 2500 and wl_max >= 800:
                functional_regions.extend([
                    (800, 1100),   # NIR short: 3rd overtones
                    (1100, 1400),  # NIR: 2nd overtones C-H, O-H, N-H
                    (1400, 1800),  # NIR: 1st overtones + combinations
                    (1800, 2100),  # NIR: C-H, O-H combinations
                    (2100, 2500)   # NIR: C-H, N-H combinations
                ])

            # Mid-Infrared regions (400-4000 cm⁻¹) - Fundamental vibrations
            if wl_min <= 4000 and wl_max >= 400:
                functional_regions.extend([
                    (400, 700),    # Far-IR: Heavy atom vibrations, lattice modes
                    (700, 1000),   # IR: C-C, C-N, C-O bending
                    (1000, 1300),  # IR: C-O stretch, C-N stretch
                    (1300, 1500),  # IR: C-H bending, CH₂/CH₃ deformation
                    (1500, 1700),  # IR: C=C, C=N, aromatic C=C
                    (1700, 1800),  # IR: C=O stretch (carbonyls)
                    (2800, 3000),  # IR: C-H stretch (alkyl)
                    (3000, 3200),  # IR: C-H stretch (aromatic, alkene)
                    (3200, 3600),  # IR: O-H, N-H stretch (broad)
                    (3600, 4000)   # IR: O-H stretch (sharp, free)
                ])

            # Select variables from functional group regions
            selected_indices = []
            for start_wl, end_wl in functional_regions:
                mask = (wavelengths >= start_wl) & (wavelengths <= end_wl)
                indices = np.where(mask)[0]
                if len(indices) > 0:
                    # Select representative points from each region
                    # More points for larger regions, minimum 2 points per region
                    n_points = max(2, min(len(indices), len(indices) // 10 + 2))
                    step = max(1, len(indices) // n_points)
                    selected_indices.extend(indices[::step][:n_points])

            selected_indices = sorted(list(set(selected_indices)))

        else:
            # Default: select variables with highest variance
            variances = x_train_preprocessed.var(axis=0).values
            n_select = min(20, n_variables // 4)
            selected_indices = np.argsort(variances)[-n_select:].tolist()

        return sorted(selected_indices)

    def _show_selection_results(self) -> None:
        """Show variable selection results."""
        results = self.session.get("variable_selection_results")

        st.markdown("---")
        st.markdown("### 📊 Variable Selection Results")

        # Summary metrics
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric("Method", results['method_name'])
        with col2:
            st.metric("Variables Selected", results['n_variables'])
        with col3:
            original_vars = self.session.get('x_train_preprocessed').shape[1]
            reduction = 100 * (1 - results['n_variables'] / original_vars)
            st.metric("Data Reduction", f"{reduction:.1f}%")
        with col4:
            st.metric("Selection Efficiency", f"{results['n_variables'] / original_vars:.3f}")

        # Visualization
        self._create_selection_visualization(results)

        # Apply selection button
        st.markdown("---")
        col1, col2 = st.columns([3, 1])

        with col1:
            st.info("Apply the variable selection to proceed to model building.")

        with col2:
            if st.button("✅ Apply Selection", type="primary"):
                # Store selected variables
                self.session.set("selected_variables", results['selected_variables'])
                self.session.set("variable_selection_complete", True)
                self.session.set("variable_selection_applied", True)  # This is what Step 5 expects!

                # Create reduced dataset
                x_train_preprocessed = self.session.get("x_train_preprocessed")
                x_train_selected = x_train_preprocessed.iloc[:, results['selected_variables']]
                self.session.set("x_train_selected", x_train_selected)

                # Step 4 completion is tracked by variable_selection_applied flag

                st.success("✅ Variable selection applied successfully!")
                st.rerun()

    def _create_selection_visualization(self, results: Dict[str, Any]) -> None:
        """Create visualization of selected variables."""
        x_train_preprocessed = self.session.get("x_train_preprocessed")
        selected_variables = results['selected_variables']

        # Create wavelength/variable indices
        try:
            wavelengths = x_train_preprocessed.columns.astype(float)
            x_label = "Wavelength (nm)"
        except:
            wavelengths = np.arange(len(x_train_preprocessed.columns))
            x_label = "Variable Index"

        # Create selection mask
        selection_mask = np.zeros(len(wavelengths), dtype=bool)
        selection_mask[selected_variables] = True

        # Create plot
        fig = go.Figure()

        # Plot all variables (gray)
        fig.add_trace(go.Scatter(
            x=wavelengths,
            y=np.ones(len(wavelengths)),
            mode='markers',
            marker=dict(color='lightgray', size=6, opacity=0.6),
            name='Excluded Variables'
        ))

        # Plot selected variables (blue)
        selected_wavelengths = wavelengths[selection_mask]
        fig.add_trace(go.Scatter(
            x=selected_wavelengths,
            y=np.ones(len(selected_wavelengths)),
            mode='markers',
            marker=dict(color='#1f77b4', size=10, opacity=0.8),
            name='Selected Variables'
        ))

        # Add mean spectrum overlay
        mean_spectrum = x_train_preprocessed.mean(axis=0)
        normalized_spectrum = (mean_spectrum - mean_spectrum.min()) / (mean_spectrum.max() - mean_spectrum.min())

        fig.add_trace(go.Scatter(
            x=wavelengths,
            y=normalized_spectrum,
            mode='lines',
            line=dict(color='rgba(100, 100, 100, 0.3)', width=1),
            name='Mean Spectrum',
            yaxis='y2'
        ))

        fig.update_layout(
            title=f"Variable Selection Results - {results['method_name']}",
            xaxis_title=x_label,
            yaxis_title="Selection Status",
            yaxis=dict(tickvals=[0, 1], ticktext=['Excluded', 'Selected'], range=[-0.2, 1.2]),
            yaxis2=dict(title="Normalized Absorbance", overlaying='y', side='right', range=[0, 1]),
            height=400,
            hovermode='x unified'
        )

        st.plotly_chart(fig, use_container_width=True)

    def _show_completed_selection(self) -> None:
        """Show completed variable selection summary."""
        st.success("✅ Variable selection completed!")

        selected_variables = self.session.get("selected_variables", [])
        method_name = self.session.get("selected_method_name", "Unknown")

        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Method Used", method_name)
        with col2:
            st.metric("Variables Selected", len(selected_variables))
        with col3:
            original_vars = self.session.get('x_train').shape[1]
            reduction = 100 * (1 - len(selected_variables) / original_vars)
            st.metric("Data Reduction", f"{reduction:.1f}%")

    def _render_navigation_section(self) -> None:
        """Render navigation buttons."""
        st.markdown("---")

        # Check if variable selection is complete
        selection_complete = self.session.has("variable_selection_complete")

        col1, col2, col3 = st.columns([1, 2, 1])

        with col1:
            if st.button("🔙 Previous: Preprocessing", key="nav_previous", use_container_width=True):
                self.session.set_current_step(3)
                st.rerun()

        with col3:
            if selection_complete:
                if st.button("➡️ Next: Model Selection", type="primary", key="nav_next", use_container_width=True):
                    self.session.set_current_step(5)
                    st.rerun()
            else:
                st.button("Complete Variable Selection First", key="nav_next_disabled", disabled=True, use_container_width=True)

    def render_prerequisites_warning(self) -> bool:
        """Check and warn about prerequisites."""
        issues = []
        if not self.session.has("x_train"):
            issues.append("Training data (X_train) is required")
        if not self.session.has("y_train"):
            issues.append("Response data (Y_train) is required")
        if not self.session.has("x_train_preprocessed"):
            issues.append("Preprocessed data is required - complete Step 3 first")

        if issues:
            st.error("❌ Prerequisites not met:")
            for issue in issues:
                st.write(f"• {issue}")
            return False
        return True
