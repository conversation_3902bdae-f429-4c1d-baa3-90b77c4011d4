"""
Step 5: Model Selection & Cross-Validation
Completely redesigned with dynamic stages, professional layouts, and cohesive color schemes.
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from typing import Dict, Any, List, Tuple, Optional
from steps.base_step import BaseStep
from utils.chatgpt_helper import Chat<PERSON>THelper
from sklearn.cross_decomposition import PLSRegression
from sklearn.model_selection import (
    cross_val_score, KFold, GridSearchCV, StratifiedKFold,
    LeaveOneOut, LeavePOut, RepeatedKFold, ShuffleSplit,
    TimeSeriesSplit, GroupKFold, cross_validate
)
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.neural_network import MLPRegressor
from sklearn.svm import SVR, NuSVR
from sklearn.preprocessing import StandardScaler
from sklearn.pipeline import Pipeline
from config.settings import CV_METHODS
from utils.nipals_pls import NIPALSRegression
from utils.enhanced_ann import EnhancedANN
from utils.model_diagnostics import ModelDiagnostics
import warnings
warnings.filterwarnings('ignore')

# Optional imports for advanced models
try:
    import xgboost as xgb
    HAS_XGBOOST = True
except ImportError:
    HAS_XGBOOST = False

try:
    from sklearn.cross_decomposition import PLSRegression
    HAS_PLS = True
except ImportError:
    HAS_PLS = False

# Color scheme constants for consistent design
COLORS = {
    'primary': '#1976D2',
    'primary_light': '#42A5F5',
    'primary_bg': 'linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)',
    'success': '#4CAF50',
    'success_light': '#66BB6A',
    'success_bg': 'linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%)',
    'warning': '#FF9800',
    'warning_light': '#FFB74D',
    'warning_bg': 'linear-gradient(135deg, #fff3e0 0%, #ffcc80 100%)',
    'error': '#F44336',
    'error_light': '#EF5350',
    'error_bg': 'linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%)',
    'info': '#2196F3',
    'info_light': '#64B5F6',
    'info_bg': 'linear-gradient(135deg, #e1f5fe 0%, #b3e5fc 100%)',
    'neutral': '#757575',
    'neutral_light': '#BDBDBD',
    'neutral_bg': 'linear-gradient(135deg, #f5f5f5 0%, #eeeeee 100%)',
    'pls': '#2E8B57',
    'pls_bg': 'linear-gradient(135deg, #f0fff0 0%, #e6ffe6 100%)',
    'neural': '#4169E1',
    'neural_bg': 'linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%)',
    'svm': '#FF6B35',
    'svm_bg': 'linear-gradient(135deg, #fff5f0 0%, #ffe6d9 100%)',
    'ensemble': '#9C27B0',
    'ensemble_bg': 'linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%)'
}


class Step5ModelSelection(BaseStep):
    """Step 5: Model Selection with manual and automatic routes."""

    def __init__(self):
        super().__init__(step_number=5, step_name="Model Selection")

    def _create_model_card(self, title: str, subtitle: str, icon: str, characteristics: List[str],
                          best_for: List[str], border_color: str, bg_gradient: str,
                          text_color: str, height: str = "auto") -> str:
        """Create a standardized model card with responsive design and dynamic height."""
        characteristics_html = "<br>".join([f"• {char}" for char in characteristics])
        best_for_html = "<br>".join([f"• {item}" for item in best_for])

        # Calculate dynamic height based on content
        content_lines = len(characteristics) + len(best_for) + 4  # Base lines for headers and spacing
        min_height = max(320, content_lines * 20 + 180)  # Dynamic height calculation

        # Create a clean HTML structure for model card with proper formatting
        model_card_html = f"""
        <div style="
            padding: 1.8rem;
            border: 3px solid {border_color};
            border-radius: 15px;
            background: {bg_gradient};
            margin-bottom: 1.2rem;
            min-height: {min_height}px;
            height: auto;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.12);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            font-family: 'Nunito Sans', sans-serif;
            word-wrap: break-word;
            overflow-wrap: break-word;
        ">
            <div style="text-align: center; margin-bottom: 1.5rem;">
                <div style="font-size: 2.8rem; margin-bottom: 0.8rem; line-height: 1;">{icon}</div>
                <div style="font-size: 1.4rem; font-weight: 700; color: {text_color}; margin-bottom: 0.6rem; line-height: 1.3;">{title}</div>
                <div style="font-size: 1rem; color: {text_color}; font-weight: 500; opacity: 0.85; line-height: 1.4;">{subtitle}</div>
            </div>
            <div style="flex-grow: 1; display: flex; flex-direction: column; gap: 1.2rem; font-size: 0.9rem; color: #2F4F4F; line-height: 1.6;">
                <div style="background: rgba(255, 255, 255, 0.3); padding: 1rem; border-radius: 10px; border-left: 4px solid {text_color};">
                    <div style="font-weight: 700; color: {text_color}; margin-bottom: 0.8rem; font-size: 0.95rem;">✨ Characteristics:</div>
                    <div style="margin-left: 0.5rem; line-height: 1.7;">{characteristics_html}</div>
                </div>
                <div style="background: rgba(255, 255, 255, 0.3); padding: 1rem; border-radius: 10px; border-left: 4px solid {text_color};">
                    <div style="font-weight: 700; color: {text_color}; margin-bottom: 0.8rem; font-size: 0.95rem;">🎯 Best for:</div>
                    <div style="margin-left: 0.5rem; line-height: 1.7;">{best_for_html}</div>
                </div>
            </div>
        </div>
        """

        return model_card_html

    def _create_route_card(self, title: str, subtitle: str, icon: str, features: List[str],
                          border_color: str, bg_gradient: str, text_color: str, height: str = "auto") -> str:
        """Create a standardized route selection card with responsive design."""
        features_html = "".join([f"<li style='margin-bottom: 0.4rem; line-height: 1.5;'>{feature}</li>" for feature in features])

        # Calculate dynamic height based on content
        content_lines = len(features) + 3  # Base lines for headers and spacing
        min_height = max(280, content_lines * 25 + 160)  # Dynamic height calculation

        # Create a clean HTML structure for route card with proper formatting
        route_card_html = f"""
        <div style="
            padding: 1.8rem;
            border: 3px solid {border_color};
            background: {bg_gradient};
            border-radius: 15px;
            text-align: center;
            margin-bottom: 1.2rem;
            min-height: {min_height}px;
            height: auto;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.12);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            font-family: 'Nunito Sans', sans-serif;
            word-wrap: break-word;
            overflow-wrap: break-word;
        ">
            <div style="margin-bottom: 1.5rem;">
                <div style="font-size: 2.8rem; margin-bottom: 0.8rem; line-height: 1;">{icon}</div>
                <h3 style="color: {text_color}; margin: 0 0 0.6rem 0; font-size: 1.5rem; font-weight: 700; line-height: 1.3;">{title}</h3>
                <p style="color: {text_color}; margin: 0; font-size: 1.1rem; font-weight: 500; opacity: 0.85; line-height: 1.4;">{subtitle}</p>
            </div>
            <div style="flex-grow: 1; background: rgba(255, 255, 255, 0.3); padding: 1.2rem; border-radius: 12px; border-left: 4px solid {text_color}; text-align: left;">
                <div style="color: {text_color}; font-size: 1rem; font-weight: 700; margin-bottom: 1rem; text-align: center;">✨ Perfect for:</div>
                <ul style="margin: 0; padding-left: 1.5rem; line-height: 1.6; color: {text_color}; font-size: 0.95rem;">{features_html}</ul>
            </div>
        </div>
        """

        return route_card_html

    def render(self) -> None:
        """Render the completely redesigned model selection interface."""
        # Use consistent header like other steps
        self.render_header()

        # Check prerequisites
        if not self._check_prerequisites():
            return

        # Get data
        x_train_selected = self.session.get("x_train_selected")
        y_train = self.session.get("y_train")

        # Check if model selection is already complete
        if self.session.has("model_selection_complete"):
            self._show_completed_model_selection()
            self._render_streamlined_navigation()
        else:
            # Render the streamlined model selection interface
            self._render_streamlined_model_selection(x_train_selected, y_train)



    def _render_streamlined_model_selection(self, x_train_selected: pd.DataFrame, y_train: pd.DataFrame) -> None:
        """Render streamlined model selection interface with single CV stage."""
        st.markdown("### 🤖 Model Selection & Cross-Validation")

        col1, col2 = st.columns([4, 1])
        with col1:
            st.info("🎯 Select and train your chemometric model with integrated cross-validation")
        with col2:
            help_icon_html = ChatGPTHelper.create_inline_help_icon(
                "Model Selection Overview",
                "model selection and cross-validation in chemometrics",
                "Please explain the different chemometric models and how to choose the best one for spectroscopic data analysis."
            )
            st.markdown(help_icon_html, unsafe_allow_html=True)

        # Algorithm selection
        st.markdown("#### 🔬 Select Algorithm")

        available_algorithms = {
            "NIPALS (PLS1, PLS2)": "Nonlinear Iterative Partial Least Squares - chemometric standard",
            "SIMPLS": "Statistically Inspired Modification of PLS - efficient implementation",
            "Artificial Neural Network (ANN)": "Multilayer perceptron neural network with multiple hidden layers",
            "ε-Support Vector Regression (ε-SVR)": "Support vector regression with epsilon-insensitive loss",
            "Nu-Support Vector Regression (Nu-SVR)": "Support vector regression with nu parameter",
            "XGBoost": "Gradient boosting for regression tasks" if HAS_XGBOOST else None
        }

        # Remove None values
        available_algorithms = {k: v for k, v in available_algorithms.items() if v is not None}

        selected_algorithm = st.selectbox(
            "Choose Algorithm:",
            list(available_algorithms.keys()),
            help="Select the algorithm for model training"
        )

        st.info(f"**{selected_algorithm}**: {available_algorithms[selected_algorithm]}")

        # Render algorithm parameters and configuration directly
        self._render_manual_model_configuration(selected_algorithm, x_train_selected, y_train)

        # Show results if available
        if self.session.has("model_training_results"):
            self._show_model_results()

        # Add navigation buttons
        self._render_streamlined_navigation()

    def _render_manual_model_configuration(self, algorithm: str, x_train: pd.DataFrame, y_train: pd.DataFrame) -> None:
        """Render manual model configuration interface with comprehensive CV methods."""

        # Algorithm-specific parameters
        st.markdown("#### ⚙️ Algorithm Parameters")
        params = self._render_algorithm_parameters(algorithm, x_train)

        # Comprehensive Cross-validation settings
        st.markdown("---")
        st.markdown("##### 🔄 Cross-Validation Configuration")

        # CV Method Selection
        cv_method = st.selectbox(
            "Select Cross-Validation Method:",
            list(CV_METHODS.keys()),
            help="Choose the cross-validation method that best fits your analysis needs"
        )

        # Show method information
        if cv_method in CV_METHODS:
            method_info = CV_METHODS[cv_method]

            st.markdown(f"**📋 {cv_method}**")
            st.write(f"**Description**: {method_info['description']}")
            st.write(f"**Suitable for**: {method_info['suitable_for']}")

            # Parameter configuration for selected CV method
            st.markdown("**⚙️ Method Parameters:**")
            cv_params = self._render_cv_parameters(cv_method, x_train, y_train)

            # Validation and warnings
            self._render_cv_validation_warnings(cv_method, cv_params, x_train.shape[0])

        # Train model
        st.markdown("---")
        if st.button("🚀 Train Model with Cross-Validation", type="primary", key="train_manual_model"):
            with st.spinner("Training model and performing cross-validation..."):
                try:
                    # Convert to numpy arrays
                    X = x_train.select_dtypes(include=[np.number]).values
                    y = y_train.select_dtypes(include=[np.number]).values

                    # Create CV configuration
                    cv_config = {
                        "method": cv_method,
                        "params": cv_params,
                        "mode": "manual"
                    }

                    # Run training with CV
                    results = self._train_model_with_cv(X, y, algorithm, params, cv_config)

                    # Store results
                    self.session.set("model_training_results", {
                        "algorithm": algorithm,
                        "results": results
                    })

                    st.success("✅ Model training complete!")
                    st.rerun()

                except Exception as e:
                    st.error(f"Error during model training: {str(e)}")



    def _show_model_results(self) -> None:
        """Show model training results."""
        results_data = self.session.get("model_training_results")
        algorithm = results_data["algorithm"]
        results = results_data["results"]

        st.markdown("---")
        st.markdown("### 📊 Model Training Results")

        # Performance metrics
        col1, col2, col3 = st.columns(3)

        with col1:
            st.metric("Algorithm", algorithm)
        with col2:
            st.metric("RMSECV", f"{results.get('best_rmsecv', 0):.4f}")
        with col3:
            st.metric("Train R²", f"{results.get('train_r2', 0):.4f}")

        # Show cross-validation details
        cv_method = results.get('cv_method', 'K-Fold')
        cv_params = results.get('cv_params', {})

        st.markdown("#### 🔄 Cross-Validation Details")
        col1, col2 = st.columns(2)

        with col1:
            st.info(f"**Method**: {cv_method}")

        with col2:
            if cv_params:
                params_str = ", ".join([f"{k}: {v}" for k, v in cv_params.items()])
                st.info(f"**Parameters**: {params_str}")

        # Model visualization
        self._create_model_performance_plots(results, algorithm)

        # Apply model button
        st.markdown("---")
        col1, col2 = st.columns([3, 1])

        with col1:
            st.info("Apply the trained model to proceed to the next step.")

        with col2:
            if st.button("✅ Apply Model", type="primary"):
                self.session.set("model_selection_complete", True)
                self.session.set("final_model", results.get('best_model'))
                self.session.set("final_model_params", results.get('best_params', {}))

                # Set the final model config that Step 6 expects
                self.session.set("final_model_config", {
                    "algorithm": algorithm,
                    "results": results,
                    "model_params": results.get('best_params', {})
                })

                st.success("✅ Model applied successfully!")
                st.rerun()

    def _show_completed_model_selection(self) -> None:
        """Show completed model selection summary."""
        st.success("✅ Model selection completed!")

        results_data = self.session.get("model_training_results", {})
        algorithm = results_data.get("algorithm", "Unknown")
        results = results_data.get("results", {})

        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Selected Algorithm", algorithm)
        with col2:
            st.metric("Final RMSECV", f"{results.get('best_rmsecv', 0):.4f}")
        with col3:
            st.metric("Final Train R²", f"{results.get('train_r2', 0):.4f}")

    def _render_streamlined_navigation(self) -> None:
        """Render navigation buttons for the streamlined interface."""
        st.markdown("---")

        col1, col2, col3 = st.columns([1, 2, 1])

        with col1:
            if st.button("⬅️ Previous: Variable Selection", key="nav_previous", use_container_width=True):
                self.session.set_current_step(4)
                st.rerun()

        with col3:
            # Check if model selection is complete
            if self.session.has("model_selection_complete") and self.session.get("model_selection_complete"):
                if st.button("➡️ Next: Build Model", key="nav_next", type="primary", use_container_width=True):
                    # Navigate to Step 6 (Step 5 completion is tracked by model_selection_complete flag)
                    self.session.set_current_step(6)
                    st.rerun()
            else:
                st.button("✅ Complete Model Selection First", key="nav_next_disabled", disabled=True, use_container_width=True)

    def _render_algorithm_parameters(self, algorithm: str, x_train: pd.DataFrame) -> Dict[str, Any]:
        """Render algorithm-specific optimization parameters."""
        params = {}
        n_samples, n_features = x_train.shape

        # Ensure parameters are always displayed
        if not algorithm:
            st.error("❌ No algorithm selected")
            return params

        if algorithm in ["NIPALS (PLS1, PLS2)", "SIMPLS"]:
            col1, col2, col3 = st.columns(3)

            with col1:
                params['n_components'] = st.slider(
                    "Number of Components",
                    min_value=1,
                    max_value=min(15, n_features, n_samples-2),
                    value=min(5, n_features//2),
                    help="Number of PLS components to extract"
                )

            with col2:
                if algorithm == "NIPALS (PLS1, PLS2)":
                    params['mode'] = st.selectbox(
                        "PLS Mode",
                        ["PLS1", "PLS2"],
                        help="PLS1 for single response, PLS2 for multiple responses"
                    )
                    params['max_iter'] = st.slider(
                        "Max Iterations",
                        min_value=100,
                        max_value=1000,
                        value=500,
                        help="Maximum iterations for NIPALS algorithm convergence"
                    )
                else:  # SIMPLS
                    params['scale'] = st.selectbox(
                        "Data Scaling",
                        [True, False],
                        index=0,
                        help="Whether to scale X and Y to unit variance"
                    )

            with col3:
                if algorithm == "NIPALS (PLS1, PLS2)":
                    params['tol'] = st.select_slider(
                        "Convergence Tolerance",
                        options=[1e-6, 1e-5, 1e-4, 1e-3],
                        value=1e-6,
                        help="Tolerance for NIPALS convergence"
                    )
                else:  # SIMPLS
                    params['deflation_mode'] = st.selectbox(
                        "Deflation Mode",
                        ["regression", "canonical"],
                        help="Type of deflation used in algorithm"
                    )

        elif algorithm == "Artificial Neural Network (ANN)":
            col1, col2, col3 = st.columns(3)

            with col1:
                params['hidden_layer_sizes'] = st.selectbox(
                    "Hidden Layer Architecture",
                    [(50,), (100,), (200,), (50, 50), (100, 50), (100, 100), (200, 100, 50)],
                    index=4,  # Default to (100, 50) - better architecture
                    help="Architecture of hidden layers"
                )
                params['activation'] = st.selectbox(
                    "Activation Function",
                    ['relu', 'tanh', 'logistic'],
                    index=0,
                    help="Activation function for hidden layers"
                )

            with col2:
                params['solver'] = st.selectbox(
                    "Solver",
                    ['adam', 'lbfgs', 'sgd'],
                    index=0,
                    help="Solver for weight optimization"
                )
                params['alpha'] = st.select_slider(
                    "L2 Regularization (α)",
                    options=[0.0001, 0.001, 0.01, 0.1, 1.0],
                    value=0.001,  # Better default for spectroscopy data
                    help="L2 penalty parameter"
                )
                params['learning_rate'] = st.selectbox(
                    "Learning Rate Schedule",
                    ['constant', 'invscaling', 'adaptive'],
                    index=2,  # Default to adaptive - better for convergence
                    help="Learning rate schedule for weight updates"
                )

            with col3:
                params['learning_rate_init'] = st.select_slider(
                    "Initial Learning Rate",
                    options=[0.0001, 0.001, 0.01, 0.1],
                    value=0.01,  # Better initial learning rate
                    help="Initial learning rate"
                )
                params['max_iter'] = st.slider(
                    "Max Iterations",
                    min_value=100,
                    max_value=2000,  # Allow more iterations
                    value=1000,  # Better default for convergence
                    help="Maximum iterations"
                )
                params['early_stopping'] = st.checkbox(
                    "Early Stopping",
                    value=True,
                    help="Whether to use early stopping to terminate training"
                )

        elif algorithm == "ε-Support Vector Regression (ε-SVR)":
            col1, col2, col3 = st.columns(3)

            with col1:
                params['C'] = st.select_slider(
                    "Regularization (C)",
                    options=[0.1, 1.0, 10.0, 100.0, 1000.0],
                    value=10.0,  # Lower C for ε-SVR (more regularization)
                    help="Regularization parameter"
                )
                params['epsilon'] = st.select_slider(
                    "Epsilon (ε)",
                    options=[0.01, 0.1, 0.2, 0.5, 1.0],
                    value=0.01,  # Lower epsilon for tighter fit
                    help="Epsilon in epsilon-SVR model"
                )

            with col2:
                params['kernel'] = st.selectbox(
                    "Kernel",
                    ['rbf', 'linear', 'poly', 'sigmoid'],
                    index=0,
                    help="Kernel type to use in the algorithm"
                )
                if params['kernel'] in ['rbf', 'poly', 'sigmoid']:
                    params['gamma'] = st.selectbox(
                        "Gamma",
                        ['scale', 'auto', 0.001, 0.01, 0.1, 1.0],
                        index=0,
                        help="Kernel coefficient"
                    )

            with col3:
                if params['kernel'] == 'poly':
                    params['degree'] = st.slider(
                        "Polynomial Degree",
                        min_value=2,
                        max_value=5,
                        value=3,
                        help="Degree for polynomial kernel"
                    )
                params['shrinking'] = st.checkbox(
                    "Shrinking Heuristic",
                    value=True,
                    help="Whether to use shrinking heuristic"
                )
                params['cache_size'] = st.slider(
                    "Cache Size (MB)",
                    min_value=100,
                    max_value=1000,
                    value=200,
                    help="Size of kernel cache"
                )

        elif algorithm == "Nu-Support Vector Regression (Nu-SVR)":
            col1, col2, col3 = st.columns(3)

            with col1:
                params['C'] = st.select_slider(
                    "Regularization (C)",
                    options=[0.1, 1.0, 10.0, 100.0, 1000.0],
                    value=1000.0,  # Higher C for Nu-SVR (less regularization)
                    help="Regularization parameter"
                )
                params['nu'] = st.slider(
                    "Nu (ν)",
                    min_value=0.01,
                    max_value=1.0,
                    value=0.1,  # Lower nu for tighter control
                    step=0.01,
                    help="Upper bound on fraction of training errors"
                )

            with col2:
                params['kernel'] = st.selectbox(
                    "Kernel",
                    ['rbf', 'linear', 'poly', 'sigmoid'],
                    index=2,  # Default to 'poly' for Nu-SVR
                    help="Kernel type to use in the algorithm"
                )
                if params['kernel'] in ['rbf', 'poly', 'sigmoid']:
                    params['gamma'] = st.selectbox(
                        "Gamma",
                        ['scale', 'auto', 0.001, 0.01, 0.1, 1.0],
                        index=1,  # Default to 'auto' for Nu-SVR
                        help="Kernel coefficient"
                    )

            with col3:
                if params['kernel'] == 'poly':
                    params['degree'] = st.slider(
                        "Polynomial Degree",
                        min_value=2,
                        max_value=5,
                        value=2,  # Lower degree for Nu-SVR
                        help="Degree for polynomial kernel"
                    )
                params['shrinking'] = st.checkbox(
                    "Shrinking Heuristic",
                    value=True,
                    help="Whether to use shrinking heuristic"
                )

        elif algorithm == "XGBoost" and HAS_XGBOOST:
            col1, col2, col3 = st.columns(3)

            with col1:
                params['n_estimators'] = st.slider(
                    "Number of Estimators",
                    min_value=50,
                    max_value=1000,
                    value=100,
                    step=50,
                    help="Number of boosting rounds"
                )
                params['max_depth'] = st.slider(
                    "Max Tree Depth",
                    min_value=3,
                    max_value=15,
                    value=6,
                    help="Maximum tree depth"
                )
                params['learning_rate'] = st.select_slider(
                    "Learning Rate (η)",
                    options=[0.01, 0.05, 0.1, 0.2, 0.3],
                    value=0.1,
                    help="Boosting learning rate"
                )

            with col2:
                params['subsample'] = st.slider(
                    "Subsample Ratio",
                    min_value=0.5,
                    max_value=1.0,
                    value=0.8,
                    step=0.1,
                    help="Subsample ratio of training instances"
                )
                params['colsample_bytree'] = st.slider(
                    "Feature Subsample",
                    min_value=0.5,
                    max_value=1.0,
                    value=0.8,
                    step=0.1,
                    help="Subsample ratio of features"
                )
                params['min_child_weight'] = st.slider(
                    "Min Child Weight",
                    min_value=1,
                    max_value=10,
                    value=1,
                    help="Minimum sum of instance weight in a child"
                )

            with col3:
                params['reg_alpha'] = st.select_slider(
                    "L1 Regularization (α)",
                    options=[0, 0.01, 0.1, 1.0, 10.0],
                    value=0,
                    help="L1 regularization term"
                )
                params['reg_lambda'] = st.select_slider(
                    "L2 Regularization (λ)",
                    options=[0, 0.01, 0.1, 1.0, 10.0],
                    value=1.0,
                    help="L2 regularization term"
                )
                params['gamma'] = st.select_slider(
                    "Gamma (γ)",
                    options=[0, 0.1, 0.5, 1.0, 2.0],
                    value=0,
                    help="Minimum loss reduction for split"
                )

        else:
            # Fallback case - show what algorithm was not matched and provide basic parameters
            st.error(f"❌ **Algorithm not recognized**: '{algorithm}'")
            st.write("**Available algorithm patterns:**")
            st.write("- NIPALS (PLS1, PLS2)")
            st.write("- SIMPLS")
            st.write("- Artificial Neural Network (ANN)")
            st.write("- ε-Support Vector Regression (ε-SVR)")
            st.write("- Nu-Support Vector Regression (Nu-SVR)")
            st.write("- XGBoost")

            # Provide basic fallback parameters
            st.markdown("##### ⚙️ Basic Parameters (Fallback)")
            params['n_components'] = st.slider(
                "Number of Components",
                min_value=1,
                max_value=min(10, n_features, n_samples-2),
                value=5,
                help="Number of components/factors to extract"
            )

        # Ensure we always return parameters
        if not params:
            st.warning("⚠️ No parameters configured. Using defaults.")
            params = {'n_components': 5}  # Basic fallback

        return params

    def _calculate_r2(self, y_true: np.ndarray, y_pred: np.ndarray) -> float:
        """Calculate R² with proper handling of edge cases."""
        try:
            # Ensure arrays are 1D
            y_true = y_true.ravel()
            y_pred = y_pred.ravel()

            # Calculate R²
            ss_res = np.sum((y_true - y_pred) ** 2)
            ss_tot = np.sum((y_true - np.mean(y_true)) ** 2)

            # Handle edge cases
            if ss_tot == 0:
                return 1.0 if ss_res == 0 else 0.0

            r2 = 1 - (ss_res / ss_tot)

            # Ensure R² is not unreasonably negative (cap at -1)
            return max(r2, -1.0)

        except Exception:
            return 0.0

    def _perform_manual_cv(self, model, X: np.ndarray, y: np.ndarray, cv_splitter) -> tuple:
        """Perform manual cross-validation focusing on RMSECV with robust error handling."""
        cv_scores = []
        r2_scores = []

        try:
            # Ensure y is 2D for consistency
            if y.ndim == 1:
                y = y.reshape(-1, 1)

            # Handle different CV splitters
            if hasattr(cv_splitter, 'split'):
                # Standard CV splitters
                splits = cv_splitter.split(X, y)
            else:
                # Fallback for custom splitters
                from sklearn.model_selection import KFold
                kf = KFold(n_splits=5, shuffle=True, random_state=42)
                splits = kf.split(X, y)

            for train_idx, test_idx in splits:
                try:
                    # Split data
                    X_train_fold, X_test_fold = X[train_idx], X[test_idx]
                    y_train_fold, y_test_fold = y[train_idx], y[test_idx]

                    # Clone and train model
                    from sklearn.base import clone
                    model_fold = clone(model)

                    # Handle different model types
                    if hasattr(model, 'fit'):
                        model_fold.fit(X_train_fold, y_train_fold)
                    else:
                        raise ValueError(f"Model {type(model)} does not have a fit method")

                    # Make predictions
                    y_pred_fold = model_fold.predict(X_test_fold)

                    # Ensure predictions are same shape as targets
                    if y_pred_fold.ndim == 1 and y_test_fold.ndim == 2:
                        y_pred_fold = y_pred_fold.reshape(-1, 1)
                    elif y_pred_fold.ndim == 2 and y_test_fold.ndim == 1:
                        y_test_fold = y_test_fold.reshape(-1, 1)

                    # Calculate MSE
                    mse = np.mean((y_test_fold.ravel() - y_pred_fold.ravel()) ** 2)
                    cv_scores.append(-mse)  # Negative MSE for consistency

                    # Calculate R²
                    r2 = self._calculate_r2(y_test_fold.ravel(), y_pred_fold.ravel())
                    r2_scores.append(r2)

                except Exception as fold_error:
                    st.warning(f"Error in CV fold: {str(fold_error)}")
                    # Add penalty scores for failed folds
                    cv_scores.append(-999.0)
                    r2_scores.append(-1.0)
                    continue

            # Calculate RMSECV
            if cv_scores:
                # Filter out penalty scores
                valid_scores = [score for score in cv_scores if score > -999.0]
                if valid_scores:
                    rmsecv = np.sqrt(-np.mean(valid_scores))
                else:
                    rmsecv = 999.0  # All folds failed
            else:
                rmsecv = 999.0

            return rmsecv, cv_scores

        except Exception as e:
            st.warning(f"Cross-validation failed: {str(e)}")
            return 999.0, []

    def _train_model_with_cv(self, X: np.ndarray, y: np.ndarray, algorithm: str, params: Dict[str, Any], cv_config: Dict[str, Any]) -> Dict[str, Any]:
        """Train model with comprehensive cross-validation using configured CV method."""

        # Create cross-validation splitter based on configuration
        cv_splitter = self._create_cv_splitter(cv_config, X.shape[0])

        # Check if we have multi-component Y data
        if y.ndim == 2 and y.shape[1] > 1:
            return self._train_multi_component_model(X, y, algorithm, params, cv_splitter, cv_config)
        else:
            return self._train_single_component_model(X, y, algorithm, params, cv_splitter, cv_config)

    def _train_single_component_model(self, X: np.ndarray, y: np.ndarray, algorithm: str, params: Dict[str, Any], cv_splitter, cv_config: Dict[str, Any]) -> Dict[str, Any]:
        """Train model for single component Y data with robust error handling."""

        try:
            # Check if algorithm needs data scaling for better performance
            needs_scaling = algorithm in ["Artificial Neural Network (ANN)",
                                        "ε-Support Vector Regression (ε-SVR)", "Nu-Support Vector Regression (Nu-SVR)"]

            # Create model based on algorithm with enhanced parameters
            base_model = self._create_model_from_params(algorithm, params)
            if base_model is None:
                raise ValueError(f"Failed to create model for algorithm: {algorithm}")

            # Apply data scaling pipeline if needed
            if needs_scaling:
                from sklearn.preprocessing import StandardScaler
                from sklearn.pipeline import Pipeline

                model = Pipeline([
                    ('scaler', StandardScaler()),
                    ('model', base_model)
                ])

                st.info(f"🔧 **Data Scaling Applied**: {algorithm} will use standardized data for better performance")
            else:
                model = base_model

            # Ensure y is properly shaped
            if y.ndim == 1:
                y = y.reshape(-1, 1)

            # Perform manual cross-validation for RMSECV calculation
            rmsecv, cv_scores = self._perform_manual_cv(model, X, y, cv_splitter)

            # Calculate standard deviations
            valid_cv_scores = [score for score in cv_scores if score > -999.0]
            rmsecv_std = np.std(np.sqrt(-np.array(valid_cv_scores))) if len(valid_cv_scores) > 1 else 0.0

            # Train final model on full dataset
            try:
                model.fit(X, y)
            except Exception as fit_error:
                st.warning(f"⚠️ Error fitting {algorithm}: {str(fit_error)}")
                # Try with flattened y for some algorithms
                if y.shape[1] == 1:
                    model.fit(X, y.ravel())
                else:
                    raise fit_error

            # Calculate training metrics
            try:
                y_pred_train = model.predict(X)

                # Ensure predictions are same shape as targets
                if y_pred_train.ndim == 1 and y.ndim == 2:
                    y_pred_train = y_pred_train.reshape(-1, 1)
                elif y_pred_train.ndim == 2 and y.ndim == 1:
                    y = y.reshape(-1, 1)

                train_rmse = np.sqrt(np.mean((y.ravel() - y_pred_train.ravel()) ** 2))
                train_r2 = self._calculate_r2(y.ravel(), y_pred_train.ravel())

            except Exception as pred_error:
                st.warning(f"⚠️ Error calculating training metrics for {algorithm}: {str(pred_error)}")
                train_rmse = 999.0
                train_r2 = -1.0

            return {
                'best_model': model,
                'best_rmsecv': rmsecv,
                'rmsecv_std': rmsecv_std,
                'train_rmse': train_rmse,
                'train_r2': train_r2,
                'best_params': params,
                'cv_scores': cv_scores,
                'cv_method': cv_config['method'],
                'cv_params': cv_config['params'],
                'is_multi_component': False
            }

        except Exception as e:
            st.error(f"❌ Error training {algorithm}: {str(e)}")
            # Return fallback result
            return {
                'best_model': None,
                'best_rmsecv': 999.0,
                'rmsecv_std': 0.0,
                'train_rmse': 999.0,
                'train_r2': -1.0,
                'best_params': params,
                'cv_scores': [],
                'cv_method': cv_config['method'],
                'cv_params': cv_config['params'],
                'is_multi_component': False
            }

    def _train_multi_component_model(self, X: np.ndarray, y: np.ndarray, algorithm: str, params: Dict[str, Any], cv_splitter, cv_config: Dict[str, Any]) -> Dict[str, Any]:
        """Train model for multi-component Y data with component-specific results and robust error handling."""

        try:
            _, n_components = y.shape

            # Get component names
            y_train_df = self.session.get("y_train")
            if y_train_df is not None and hasattr(y_train_df, 'columns'):
                component_names = list(y_train_df.columns)
            else:
                component_names = [f"Component {i+1}" for i in range(n_components)]

            # Initialize storage for component-specific results
            component_results = []
            overall_cv_scores = []

            # Train model for each component separately
            for comp_idx in range(n_components):
                try:
                    comp_name = component_names[comp_idx]
                    y_comp = y[:, comp_idx].reshape(-1, 1)  # Single component

                    # Check if algorithm needs data scaling for better performance
                    needs_scaling = algorithm in ["Artificial Neural Network (ANN)",
                                                "ε-Support Vector Regression (ε-SVR)", "Nu-Support Vector Regression (Nu-SVR)"]

                    # Create model for this component
                    base_model_comp = self._create_model_from_params(algorithm, params)
                    if base_model_comp is None:
                        st.warning(f"⚠️ Failed to create model for component {comp_name}")
                        continue

                    # Apply data scaling pipeline if needed
                    if needs_scaling:
                        from sklearn.preprocessing import StandardScaler
                        from sklearn.pipeline import Pipeline

                        model_comp = Pipeline([
                            ('scaler', StandardScaler()),
                            ('model', base_model_comp)
                        ])
                    else:
                        model_comp = base_model_comp

                    # Perform cross-validation for this component
                    rmsecv_comp, cv_scores_comp = self._perform_manual_cv(model_comp, X, y_comp, cv_splitter)

                    # Train final model for this component
                    try:
                        model_comp.fit(X, y_comp)
                    except Exception as fit_error:
                        st.warning(f"⚠️ Error fitting {algorithm} for component {comp_name}: {str(fit_error)}")
                        # Try with flattened y
                        model_comp.fit(X, y_comp.ravel())

                    # Calculate training metrics for this component
                    try:
                        y_pred_comp = model_comp.predict(X)

                        # Ensure predictions are same shape as targets
                        if y_pred_comp.ndim == 1 and y_comp.ndim == 2:
                            y_pred_comp = y_pred_comp.reshape(-1, 1)

                        train_rmse_comp = np.sqrt(np.mean((y_comp.ravel() - y_pred_comp.ravel()) ** 2))
                        train_r2_comp = self._calculate_r2(y_comp.ravel(), y_pred_comp.ravel())

                    except Exception as pred_error:
                        st.warning(f"⚠️ Error calculating metrics for component {comp_name}: {str(pred_error)}")
                        train_rmse_comp = 999.0
                        train_r2_comp = -1.0

                    # Store component results
                    valid_cv_scores = [score for score in cv_scores_comp if score > -999.0]
                    component_results.append({
                        'component_name': comp_name,
                        'component_index': comp_idx,
                        'model': model_comp,
                        'rmsecv': rmsecv_comp,
                        'train_rmse': train_rmse_comp,
                        'train_r2': train_r2_comp,
                        'cv_scores': cv_scores_comp,
                        'rmsecv_std': np.std(np.sqrt(-np.array(valid_cv_scores))) if len(valid_cv_scores) > 1 else 0.0
                    })

                    # Accumulate for overall metrics
                    overall_cv_scores.extend(cv_scores_comp)

                except Exception as comp_error:
                    st.warning(f"⚠️ Error processing component {comp_idx}: {str(comp_error)}")
                    continue

            if not component_results:
                raise ValueError("No components were successfully processed")

            # Calculate overall metrics (averaged across components)
            overall_rmsecv = np.mean([comp['rmsecv'] for comp in component_results])
            overall_train_rmse = np.mean([comp['train_rmse'] for comp in component_results])
            overall_train_r2 = np.mean([comp['train_r2'] for comp in component_results])

            # Calculate overall standard deviations
            overall_rmsecv_std = np.std([comp['rmsecv'] for comp in component_results])

            # Train a combined model for overall predictions (if needed)
            try:
                combined_model = self._create_model_from_params(algorithm, params)
                combined_model.fit(X, y)
            except Exception as combined_error:
                st.warning(f"⚠️ Error training combined model: {str(combined_error)}")
                # Use the first component model as fallback
                combined_model = component_results[0]['model'] if component_results else None

            return {
                'best_model': combined_model,  # Combined model for overall predictions
                'best_rmsecv': overall_rmsecv,
                'rmsecv_std': overall_rmsecv_std,
                'train_rmse': overall_train_rmse,
                'train_r2': overall_train_r2,
                'best_params': params,
                'cv_scores': overall_cv_scores,
                'cv_method': cv_config['method'],
                'cv_params': cv_config['params'],
                'is_multi_component': True,
                'n_components': n_components,
                'component_names': component_names,
                'component_results': component_results
            }

        except Exception as e:
            st.error(f"❌ Error training multi-component {algorithm}: {str(e)}")
            # Return fallback result
            return {
                'best_model': None,
                'best_rmsecv': 999.0,
                'rmsecv_std': 0.0,
                'train_rmse': 999.0,
                'train_r2': -1.0,
                'best_params': params,
                'cv_scores': [],
                'cv_method': cv_config['method'],
                'cv_params': cv_config['params'],
                'is_multi_component': True,
                'n_components': 0,
                'component_names': [],
                'component_results': []
            }

    def _create_cv_splitter(self, cv_config: Dict[str, Any], n_samples: int):
        """Create cross-validation splitter based on configuration."""
        cv_method = cv_config['method']
        cv_params = cv_config['params']

        if cv_method == "K-Fold":
            from sklearn.model_selection import KFold
            return KFold(
                n_splits=cv_params.get('n_splits', 5),
                shuffle=cv_params.get('shuffle', True),
                random_state=cv_params.get('random_state', 42)
            )

        elif cv_method == "Stratified K-Fold":
            from sklearn.model_selection import StratifiedKFold
            return StratifiedKFold(
                n_splits=cv_params.get('n_splits', 5),
                shuffle=cv_params.get('shuffle', True),
                random_state=cv_params.get('random_state', 42)
            )

        elif cv_method == "Leave-One-Out":
            from sklearn.model_selection import LeaveOneOut
            return LeaveOneOut()

        elif cv_method == "Leave-P-Out":
            from sklearn.model_selection import LeavePOut
            return LeavePOut(p=cv_params.get('p', 2))

        elif cv_method == "Repeated K-Fold":
            from sklearn.model_selection import RepeatedKFold
            return RepeatedKFold(
                n_splits=cv_params.get('n_splits', 5),
                n_repeats=cv_params.get('n_repeats', 3),
                random_state=cv_params.get('random_state', 42)
            )

        elif cv_method == "Shuffle Split":
            from sklearn.model_selection import ShuffleSplit
            return ShuffleSplit(
                n_splits=cv_params.get('n_splits', 10),
                test_size=cv_params.get('test_size', 0.2),
                train_size=cv_params.get('train_size', None),
                random_state=cv_params.get('random_state', 42)
            )

        elif cv_method == "Time Series Split":
            from sklearn.model_selection import TimeSeriesSplit
            return TimeSeriesSplit(
                n_splits=cv_params.get('n_splits', 5),
                max_train_size=cv_params.get('max_train_size', None),
                test_size=cv_params.get('test_size', None),
                gap=cv_params.get('gap', 0)
            )

        elif cv_method == "Group K-Fold":
            from sklearn.model_selection import GroupKFold
            return GroupKFold(n_splits=cv_params.get('n_splits', 5))

        elif cv_method == "Monte Carlo Cross-Validation":
            # Use ShuffleSplit for Monte Carlo CV
            from sklearn.model_selection import ShuffleSplit
            return ShuffleSplit(
                n_splits=cv_params.get('n_splits', 100),
                test_size=cv_params.get('test_size', 0.2),
                random_state=cv_params.get('random_state', 42)
            )

        else:
            # Default to K-Fold
            from sklearn.model_selection import KFold
            return KFold(n_splits=5, shuffle=True, random_state=42)

    def _create_model_from_params(self, algorithm: str, params: Dict[str, Any]):
        """Create model instance from algorithm and parameters with robust error handling."""

        try:
            if algorithm == "NIPALS (PLS1, PLS2)":
                from utils.nipals_pls import NIPALSRegression
                return NIPALSRegression(
                    n_components=params.get('n_components', 5),
                    mode=params.get('mode', 'PLS1'),
                    max_iter=params.get('max_iter', 500),
                    tol=params.get('tol', 1e-6)
                )

            elif algorithm == "SIMPLS":
                # Use PLSRegression with different parameters to differentiate from NIPALS
                return PLSRegression(
                    n_components=params.get('n_components', 5),
                    scale=params.get('scale', True),
                    max_iter=params.get('max_iter', 500),  # Different from NIPALS
                    tol=1e-06,  # Fixed tolerance for SIMPLS
                    copy=True
                )

            elif algorithm == "Artificial Neural Network (ANN)":
                # Ensure validation_fraction is set correctly
                early_stopping = params.get('early_stopping', True)
                validation_fraction = params.get('validation_fraction', 0.1) if early_stopping else 0.1

                return MLPRegressor(
                    hidden_layer_sizes=params.get('hidden_layer_sizes', (100, 50)),  # Better default architecture
                    activation=params.get('activation', 'relu'),
                    solver=params.get('solver', 'adam'),
                    alpha=params.get('alpha', 0.001),  # Better regularization for spectroscopy data
                    learning_rate=params.get('learning_rate', 'adaptive'),  # Better learning rate schedule
                    learning_rate_init=params.get('learning_rate_init', 0.01),  # Better initial learning rate
                    max_iter=params.get('max_iter', 1000),  # More iterations for convergence
                    early_stopping=early_stopping,
                    validation_fraction=validation_fraction,
                    n_iter_no_change=20,  # More patience for convergence
                    random_state=42,
                    warm_start=False
                )

            elif algorithm == "ε-Support Vector Regression (ε-SVR)":
                return SVR(
                    C=params.get('C', 10.0),  # Lower C for ε-SVR (more regularization)
                    epsilon=params.get('epsilon', 0.01),  # Lower epsilon for tighter fit
                    kernel=params.get('kernel', 'rbf'),
                    gamma=params.get('gamma', 'scale'),
                    degree=params.get('degree', 3),
                    shrinking=params.get('shrinking', True),
                    cache_size=params.get('cache_size', 500),  # More cache for better performance
                    max_iter=params.get('max_iter', -1)
                )

            elif algorithm == "Nu-Support Vector Regression (Nu-SVR)":
                return NuSVR(
                    C=params.get('C', 1000.0),  # Higher C for Nu-SVR (less regularization)
                    nu=params.get('nu', 0.1),  # Lower nu for tighter control
                    kernel=params.get('kernel', 'poly'),  # Different default kernel
                    gamma=params.get('gamma', 'auto'),  # Different gamma setting
                    degree=params.get('degree', 2),  # Lower degree for poly kernel
                    shrinking=params.get('shrinking', True),
                    cache_size=params.get('cache_size', 500),  # More cache for better performance
                    max_iter=params.get('max_iter', -1)
                )

            elif algorithm == "XGBoost" and HAS_XGBOOST:
                import xgboost as xgb
                return xgb.XGBRegressor(
                    n_estimators=params.get('n_estimators', 100),
                    max_depth=params.get('max_depth', 6),
                    learning_rate=params.get('learning_rate', 0.1),
                    subsample=params.get('subsample', 0.8),
                    colsample_bytree=params.get('colsample_bytree', 0.8),
                    min_child_weight=params.get('min_child_weight', 1),
                    reg_alpha=params.get('reg_alpha', 0),
                    reg_lambda=params.get('reg_lambda', 1.0),
                    gamma=params.get('gamma', 0),
                    random_state=42,
                    n_jobs=1,  # Avoid multiprocessing issues
                    verbosity=0  # Reduce output
                )

            else:
                # Fallback to PLS
                st.warning(f"⚠️ Algorithm '{algorithm}' not recognized. Using SIMPLS as fallback.")
                return PLSRegression(n_components=5, scale=True)

        except Exception as e:
            st.error(f"❌ Error creating model for {algorithm}: {str(e)}")
            # Return fallback model
            return PLSRegression(n_components=5, scale=True)

    def _create_model_performance_plots(self, results: Dict[str, Any], algorithm: str) -> None:
        """Create model performance visualization plots."""
        # Get data for plotting
        x_train_selected = self.session.get("x_train_selected")
        y_train = self.session.get("y_train")

        if x_train_selected is None or y_train is None:
            st.warning("⚠️ Data not available for visualization")
            return

        # Convert to numpy arrays
        X = x_train_selected.select_dtypes(include=[np.number]).values
        y = y_train.select_dtypes(include=[np.number]).values

        # Check if this is multi-component
        is_multi_component = results.get('is_multi_component', False)

        if is_multi_component:
            self._create_multi_component_performance_plots(results, X, y)
        else:
            self._create_single_component_performance_plots(results, X, y)

    def _create_single_component_performance_plots(self, results: Dict[str, Any], X: np.ndarray, y: np.ndarray) -> None:
        """Create performance plots for single component models."""
        # Get model and make predictions
        model = results.get('best_model')
        if model is None:
            st.warning("⚠️ Model not available for visualization")
            return

        try:
            y_pred = model.predict(X)

            # Create plots
            col1, col2 = st.columns(2)

            with col1:
                # Measured vs Predicted plot
                fig = go.Figure()

                fig.add_trace(go.Scatter(
                    x=y.ravel(),
                    y=y_pred.ravel(),
                    mode='markers',
                    marker=dict(color='blue', size=8, opacity=0.7),
                    name='Predictions'
                ))

                # Add perfect prediction line
                min_val = min(y.min(), y_pred.min())
                max_val = max(y.max(), y_pred.max())
                fig.add_trace(go.Scatter(
                    x=[min_val, max_val],
                    y=[min_val, max_val],
                    mode='lines',
                    line=dict(color='red', dash='dash'),
                    name='Perfect Prediction'
                ))

                fig.update_layout(
                    title="Measured vs Predicted",
                    xaxis_title="Measured Values",
                    yaxis_title="Predicted Values",
                    height=400
                )

                st.plotly_chart(fig, use_container_width=True)

            with col2:
                # Residuals plot
                residuals = y.ravel() - y_pred.ravel()

                fig = go.Figure()

                fig.add_trace(go.Scatter(
                    x=y_pred.ravel(),
                    y=residuals,
                    mode='markers',
                    marker=dict(color='green', size=8, opacity=0.7),
                    name='Residuals'
                ))

                # Add zero line
                fig.add_hline(y=0, line_dash="dash", line_color="red")

                fig.update_layout(
                    title="Residuals Plot",
                    xaxis_title="Predicted Values",
                    yaxis_title="Residuals",
                    height=400
                )

                st.plotly_chart(fig, use_container_width=True)

        except Exception as e:
            st.warning(f"⚠️ Could not create performance plots: {str(e)}")

    def _create_multi_component_performance_plots(self, results: Dict[str, Any], X: np.ndarray, y: np.ndarray) -> None:
        """Create performance plots for multi-component models."""
        component_results = results.get('component_results', [])
        component_names = results.get('component_names', [])

        if not component_results:
            st.warning("⚠️ Component results not available for visualization")
            return

        st.markdown("#### 📊 Component-Specific Performance Analysis")

        # Component selector
        selected_component = st.selectbox(
            "Select Component to Visualize:",
            options=range(len(component_names)),
            format_func=lambda x: f"{component_names[x]} (Component {x+1})",
            key="component_selector"
        )

        # Get selected component data
        comp_result = component_results[selected_component]
        comp_model = comp_result['model']
        comp_name = comp_result['component_name']

        # Get component-specific y data
        y_comp = y[:, selected_component].reshape(-1, 1)

        try:
            y_pred_comp = comp_model.predict(X)

            # Create component-specific plots
            col1, col2 = st.columns(2)

            with col1:
                # Measured vs Predicted plot for selected component
                fig = go.Figure()

                fig.add_trace(go.Scatter(
                    x=y_comp.ravel(),
                    y=y_pred_comp.ravel(),
                    mode='markers',
                    marker=dict(color='blue', size=8, opacity=0.7),
                    name=f'{comp_name} Predictions'
                ))

                # Add perfect prediction line
                min_val = min(y_comp.min(), y_pred_comp.min())
                max_val = max(y_comp.max(), y_pred_comp.max())
                fig.add_trace(go.Scatter(
                    x=[min_val, max_val],
                    y=[min_val, max_val],
                    mode='lines',
                    line=dict(color='red', dash='dash'),
                    name='Perfect Prediction'
                ))

                fig.update_layout(
                    title=f"Measured vs Predicted - {comp_name}",
                    xaxis_title="Measured Values",
                    yaxis_title="Predicted Values",
                    height=400
                )

                st.plotly_chart(fig, use_container_width=True)

            with col2:
                # Residuals plot for selected component
                residuals_comp = y_comp.ravel() - y_pred_comp.ravel()

                fig = go.Figure()

                fig.add_trace(go.Scatter(
                    x=y_pred_comp.ravel(),
                    y=residuals_comp,
                    mode='markers',
                    marker=dict(color='green', size=8, opacity=0.7),
                    name=f'{comp_name} Residuals'
                ))

                # Add zero line
                fig.add_hline(y=0, line_dash="dash", line_color="red")

                fig.update_layout(
                    title=f"Residuals Plot - {comp_name}",
                    xaxis_title="Predicted Values",
                    yaxis_title="Residuals",
                    height=400
                )

                st.plotly_chart(fig, use_container_width=True)

            # Component performance summary
            st.markdown("##### 📈 Component Performance Summary")

            # Create summary table
            summary_data = []
            for i, comp_res in enumerate(component_results):
                summary_data.append({
                    'Component': comp_res['component_name'],
                    'RMSECV': f"{comp_res['rmsecv']:.4f}",
                    'Train RMSE': f"{comp_res['train_rmse']:.4f}",
                    'Train R²': f"{comp_res['train_r2']:.4f}"
                })

            import pandas as pd
            summary_df = pd.DataFrame(summary_data)
            st.dataframe(summary_df, use_container_width=True)

            # Overall performance comparison
            st.markdown("##### 🎯 Overall Performance Comparison")
            col1, col2, col3 = st.columns(3)

            with col1:
                st.metric("Overall RMSECV", f"{results.get('best_rmsecv', 0):.4f}")
            with col2:
                st.metric("Overall Train RMSE", f"{results.get('train_rmse', 0):.4f}")
            with col3:
                st.metric("Overall Train R²", f"{results.get('train_r2', 0):.4f}")

        except Exception as e:
            st.warning(f"⚠️ Could not create component performance plots: {str(e)}")

    def _check_prerequisites(self) -> bool:
        """Check if all prerequisites are met."""
        missing_items = []

        if not self.session.has("x_train_selected"):
            missing_items.append("Selected training data (X_train)")
        if not self.session.has("y_train"):
            missing_items.append("Training targets (Y_train)")
        if not self.session.has("variable_selection_applied"):
            missing_items.append("Variable selection completion")

        if missing_items:
            st.error("❌ **Prerequisites Missing**")
            st.write("Please complete the following steps before proceeding:")
            for item in missing_items:
                st.write(f"• {item}")

            if st.button("🔄 Refresh", key="refresh_prerequisites"):
                st.rerun()
            return False

        return True

    def _render_simplified_stage_navigation(self, current_stage: int) -> None:
        """Render simplified stage navigation without the workflow header."""
        # Add help icon in the top right
        col1, col2 = st.columns([6, 1])
        with col2:
            help_icon_html = ChatGPTHelper.create_inline_help_icon(
                "Model Selection & Cross-Validation",
                "understanding comprehensive model selection in chemometrics",
                """Please explain the comprehensive approach to model selection and cross-validation in chemometrics:

1. **Model Categories**: What are the main types of chemometric models (PLS, Neural Networks, Support Vector Models, Ensemble Methods)?
2. **Algorithm Selection**: How do I choose between different algorithms within each category?
3. **Optimization Strategies**: What are the differences between manual configuration and automated grid search?
4. **Cross-Validation**: How do I select appropriate cross-validation methods for spectroscopic data?
5. **Model Evaluation**: What metrics should I use to evaluate model performance?
6. **Best Practices**: What are the best practices for model selection in chemometric analysis?

Please provide practical guidance for developing robust chemometric models."""
            )
            st.markdown(help_icon_html, unsafe_allow_html=True)

        # Dynamic stage indicators
        stages = [
            ("🏷️", "Model Category", "Choose model type", 1),
            ("🤖", "Algorithm", "Select specific algorithm", 2),
            ("🔄", "Cross-Validation", "Configure CV method", 3),
            ("⚙️", "Optimization", "Configure & train model", 4),
            ("📊", "Results", "View training results", 5)
        ]

        # Check accessible stages
        accessible_stages = self._get_accessible_stages()

        # Create responsive columns
        cols = st.columns(len(stages), gap="medium")

        for i, (col, (icon, stage_name, description, stage_num)) in enumerate(zip(cols, stages)):
            with col:
                is_current = stage_num == current_stage
                is_completed = stage_num < current_stage
                is_accessible = stage_num in accessible_stages

                if is_current:
                    # Current stage - highlighted
                    st.markdown(f"""
                    <div style="
                        padding: 1rem;
                        background: {COLORS['primary_bg']};
                        border: 3px solid {COLORS['primary']};
                        border-radius: 12px;
                        text-align: center;
                        box-shadow: 0 4px 12px rgba(25, 118, 210, 0.2);
                        transform: scale(1.05);
                        transition: all 0.3s ease;
                    ">
                        <div style="font-size: 2rem; margin-bottom: 0.5rem;">{icon}</div>
                        <div style="color: {COLORS['primary']}; font-weight: 700; font-size: 0.9rem; margin-bottom: 0.3rem;">
                            Stage {stage_num}
                        </div>
                        <div style="color: {COLORS['primary']}; font-weight: 600; font-size: 0.8rem; margin-bottom: 0.3rem;">
                            {stage_name}
                        </div>
                        <div style="color: {COLORS['primary']}; font-size: 0.7rem; opacity: 0.8; line-height: 1.2;">
                            {description}
                        </div>
                        <div style="
                            background: {COLORS['primary']};
                            color: white;
                            padding: 0.2rem 0.5rem;
                            border-radius: 8px;
                            font-size: 0.7rem;
                            font-weight: 600;
                            margin-top: 0.5rem;
                        ">
                            ACTIVE
                        </div>
                    </div>
                    """, unsafe_allow_html=True)

                elif is_completed:
                    # Completed stage - clickable
                    if st.button(
                        f"{icon}\nStage {stage_num}\n{stage_name}",
                        key=f"nav_stage_{stage_num}",
                        help=f"Return to {stage_name}: {description}",
                        use_container_width=True
                    ):
                        self.session.set("model_selection_stage", stage_num)
                        st.rerun()

                    # Completed stage styling
                    st.markdown(f"""
                    <style>
                    div[data-testid="stButton"] > button[key="nav_stage_{stage_num}"] {{
                        background: {COLORS['success_bg']} !important;
                        color: {COLORS['success']} !important;
                        border: 2px solid {COLORS['success']} !important;
                        border-radius: 12px !important;
                        font-weight: 600 !important;
                        font-size: 0.75rem !important;
                        line-height: 1.2 !important;
                        padding: 0.8rem 0.5rem !important;
                        height: auto !important;
                        min-height: 90px !important;
                        font-family: 'Nunito Sans', sans-serif !important;
                        box-shadow: 0 2px 6px rgba(76, 175, 80, 0.2) !important;
                        transition: all 0.2s ease !important;
                    }}
                    div[data-testid="stButton"] > button[key="nav_stage_{stage_num}"]:hover {{
                        background: {COLORS['success']} !important;
                        color: white !important;
                        transform: translateY(-2px) !important;
                        box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3) !important;
                    }}
                    </style>
                    """, unsafe_allow_html=True)

                elif is_accessible:
                    # Accessible future stage
                    if st.button(
                        f"{icon}\nStage {stage_num}\n{stage_name}",
                        key=f"nav_stage_{stage_num}",
                        help=f"Proceed to {stage_name}: {description}",
                        use_container_width=True
                    ):
                        self.session.set("model_selection_stage", stage_num)
                        st.rerun()

                    # Accessible stage styling
                    st.markdown(f"""
                    <style>
                    div[data-testid="stButton"] > button[key="nav_stage_{stage_num}"] {{
                        background: {COLORS['info_bg']} !important;
                        color: {COLORS['info']} !important;
                        border: 2px solid {COLORS['info_light']} !important;
                        border-radius: 12px !important;
                        font-weight: 600 !important;
                        font-size: 0.75rem !important;
                        line-height: 1.2 !important;
                        padding: 0.8rem 0.5rem !important;
                        height: auto !important;
                        min-height: 90px !important;
                        font-family: 'Nunito Sans', sans-serif !important;
                        transition: all 0.2s ease !important;
                    }}
                    div[data-testid="stButton"] > button[key="nav_stage_{stage_num}"]:hover {{
                        background: {COLORS['info']} !important;
                        color: white !important;
                        transform: translateY(-2px) !important;
                        box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3) !important;
                    }}
                    </style>
                    """, unsafe_allow_html=True)

                else:
                    # Locked future stage
                    st.markdown(f"""
                    <div style="
                        padding: 1rem;
                        background: {COLORS['neutral_bg']};
                        border: 2px solid {COLORS['neutral_light']};
                        border-radius: 12px;
                        text-align: center;
                        opacity: 0.5;
                        min-height: 90px;
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                    ">
                        <div style="font-size: 1.5rem; margin-bottom: 0.3rem;">🔒</div>
                        <div style="color: {COLORS['neutral']}; font-weight: 600; font-size: 0.8rem; margin-bottom: 0.2rem;">
                            Stage {stage_num}
                        </div>
                        <div style="color: {COLORS['neutral']}; font-size: 0.7rem;">
                            {stage_name}
                        </div>
                    </div>
                    """, unsafe_allow_html=True)

    def _get_accessible_stages(self) -> List[int]:
        """Get list of accessible stages based on current progress."""
        accessible = [1]  # Stage 1 is always accessible

        # Stage 2 accessible if model type is selected
        if self.session.has("selected_model_type"):
            accessible.append(2)

        # Stage 3 accessible if algorithm is selected
        if self.session.has("selected_algorithm"):
            accessible.append(3)

        # Stage 4 accessible if CV is configured
        if self.session.has("cv_configuration"):
            accessible.append(4)

        # Stage 5 accessible if model training results exist
        if self.session.has("model_training_results"):
            accessible.append(5)

        return accessible

    def _render_stage_header(self, stage_num: int, title: str, subtitle: str, icon: str,
                           help_topic: str, help_context: str, help_prompt: str) -> None:
        """Render a standardized stage header with help integration."""
        # Stage header container
        st.markdown(f"""
        <div style="
            background: {COLORS['primary_bg']};
            padding: 1.2rem 1.5rem;
            border-radius: 15px;
            margin-bottom: 0.8rem;
            border: 2px solid {COLORS['primary']};
            box-shadow: 0 4px 12px rgba(25, 118, 210, 0.15);
            font-family: 'Nunito Sans', sans-serif;
        ">
            <div style="display: flex; justify-content: space-between; align-items: flex-start;">
                <div style="flex: 1;">
                    <h2 style="
                        color: {COLORS['primary']};
                        margin: 0 0 0.3rem 0;
                        font-size: 1.8rem;
                        font-weight: 700;
                        display: flex;
                        align-items: center;
                        gap: 0.8rem;
                    ">
                        <span style="font-size: 2rem;">{icon}</span>
                        Stage {stage_num}: {title}
                    </h2>
                    <p style="
                        color: {COLORS['primary']};
                        margin: 0;
                        font-size: 1rem;
                        font-weight: 500;
                        opacity: 0.8;
                        line-height: 1.3;
                    ">
                        {subtitle}
                    </p>
                </div>
            </div>
        </div>
        """, unsafe_allow_html=True)

        # Help icon integrated with title
        help_icon_html = ChatGPTHelper.create_inline_help_icon(help_topic, help_context, help_prompt)
        st.markdown(f"""
        <div style="margin-bottom: 0.5rem;">
            {help_icon_html}
        </div>
        """, unsafe_allow_html=True)

    def _render_stage1_model_category_selection(self) -> None:
        """Render Stage 1: Model Category Selection with enhanced design."""
        # Stage header with professional styling
        self._render_stage_header(
            stage_num=1,
            title="Model Category Selection",
            subtitle="Choose the category of chemometric model that best fits your analytical needs",
            icon="🏷️",
            help_topic="Model Category Selection",
            help_context="understanding different categories of chemometric models",
            help_prompt="""Please explain the different categories of chemometric models and help me choose:

**Available Categories:**
1. **PLS Models** (NIPALS, SIMPLS) - Traditional chemometric approaches
2. **Neural Network Models** (MLP, BPNN) - Non-linear pattern recognition
3. **Support Vector Models** (ε-SVR, Nu-SVR) - Robust regression methods
4. **Ensemble Models** (XGBoost) - Advanced ensemble learning

**My Analysis Context:**
- Spectroscopic data analysis
- Need for model interpretability vs. predictive performance
- Dataset characteristics and complexity

Please help me understand:
1. What are the strengths and limitations of each model category?
2. Which category is most suitable for different types of spectroscopic data?
3. How do I balance interpretability vs. predictive performance?
4. What factors should I consider when choosing a model category?"""
        )

        # Model category cards with enhanced design
        st.markdown("#### 📋 Available Model Categories")

        # Create responsive 2x2 grid
        col1, col2 = st.columns(2, gap="large")

        with col1:
            # PLS Models Card
            self._render_model_category_card(
                title="PLS Models",
                subtitle="Partial Least Squares",
                icon="📊",
                characteristics=[
                    "Linear relationships in spectral data",
                    "High-dimensional, collinear variables",
                    "Traditional chemometric analysis",
                    "Interpretable latent variables"
                ],
                best_for=[
                    "NIR/IR spectroscopy",
                    "Quantitative analysis",
                    "Regulatory compliance",
                    "Method development"
                ],
                border_color=COLORS['pls'],
                bg_gradient=COLORS['pls_bg'],
                text_color=COLORS['pls'],
                button_key="select_pls_models",
                button_text="📊 Select PLS Models",
                model_type="PLS Models"
            )

            # Neural Network Models Card
            self._render_model_category_card(
                title="Neural Network Models",
                subtitle="Artificial Neural Networks",
                icon="🧠",
                characteristics=[
                    "Non-linear relationships",
                    "Complex pattern recognition",
                    "Large datasets with hidden patterns",
                    "High predictive accuracy"
                ],
                best_for=[
                    "Complex spectral patterns",
                    "Multi-component analysis",
                    "Process optimization",
                    "Advanced modeling"
                ],
                border_color=COLORS['neural'],
                bg_gradient=COLORS['neural_bg'],
                text_color=COLORS['neural'],
                button_key="select_neural_models",
                button_text="🧠 Select Neural Network Models",
                model_type="Neural Network Models"
            )

        with col2:
            # Support Vector Models Card
            self._render_model_category_card(
                title="Support Vector Models",
                subtitle="Support Vector Regression",
                icon="⚡",
                characteristics=[
                    "Robust to outliers",
                    "High-dimensional data",
                    "Non-linear kernel transformations",
                    "Good generalization capability"
                ],
                best_for=[
                    "Noisy spectral data",
                    "Small sample sizes",
                    "Outlier-prone datasets",
                    "Robust predictions"
                ],
                border_color=COLORS['svm'],
                bg_gradient=COLORS['svm_bg'],
                text_color=COLORS['svm'],
                button_key="select_svm_models",
                button_text="⚡ Select Support Vector Models",
                model_type="Support Vector Models"
            )

            # Ensemble Models Card
            ensemble_available = HAS_XGBOOST
            if ensemble_available:
                self._render_model_category_card(
                    title="Ensemble Models",
                    subtitle="Gradient Boosting",
                    icon="🌳",
                    characteristics=[
                        "Excellent predictive performance",
                        "Feature importance ranking",
                        "Handles missing values",
                        "Ensemble learning power"
                    ],
                    best_for=[
                        "Maximum accuracy",
                        "Feature selection insights",
                        "Complex relationships",
                        "Competition-grade models"
                    ],
                    border_color=COLORS['ensemble'],
                    bg_gradient=COLORS['ensemble_bg'],
                    text_color=COLORS['ensemble'],
                    button_key="select_ensemble_models",
                    button_text="🌳 Select Ensemble Models",
                    model_type="Ensemble Models"
                )
            else:
                self._render_unavailable_model_card()

        # Current selection display and navigation
        self._render_stage1_navigation()

    def _render_model_category_card(self, title: str, subtitle: str, icon: str,
                                  characteristics: List[str], best_for: List[str],
                                  border_color: str, bg_gradient: str, text_color: str,
                                  button_key: str, button_text: str, model_type: str) -> None:
        """Render a model category card with responsive design and dynamic height."""
        characteristics_html = "<br>".join([f"• {char}" for char in characteristics])
        best_for_html = "<br>".join([f"• {item}" for item in best_for])

        # Calculate dynamic height based on content
        content_lines = len(characteristics) + len(best_for) + 4  # Base lines for headers and spacing
        min_height = max(380, content_lines * 22 + 200)  # Dynamic height calculation

        # Create a clean, properly formatted HTML structure
        card_html = f"""
<div style="
    padding: 1.8rem;
    border: 3px solid {border_color};
    border-radius: 15px;
    background: {bg_gradient};
    margin-bottom: 1.2rem;
    min-height: {min_height}px;
    height: auto;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.12);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    font-family: 'Nunito Sans', sans-serif;
    word-wrap: break-word;
    overflow-wrap: break-word;
">
    <div style="text-align: center; margin-bottom: 1.5rem;">
        <div style="font-size: 3.2rem; margin-bottom: 0.8rem; line-height: 1;">{icon}</div>
        <div style="font-size: 1.5rem; font-weight: 700; color: {text_color}; margin-bottom: 0.6rem; line-height: 1.3;">{title}</div>
        <div style="font-size: 1.1rem; color: {text_color}; font-weight: 500; opacity: 0.85; line-height: 1.4;">{subtitle}</div>
    </div>
    <div style="flex-grow: 1; display: flex; flex-direction: column; gap: 1.2rem; font-size: 0.9rem; color: #2F4F4F; line-height: 1.6;">
        <div style="background: rgba(255, 255, 255, 0.3); padding: 1rem; border-radius: 10px; border-left: 4px solid {text_color};">
            <div style="font-weight: 700; color: {text_color}; margin-bottom: 0.8rem; font-size: 0.95rem;">
                ✨ Characteristics:
            </div>
            <div style="margin-left: 0.5rem; line-height: 1.7;">
                {characteristics_html}
            </div>
        </div>
        <div style="background: rgba(255, 255, 255, 0.3); padding: 1rem; border-radius: 10px; border-left: 4px solid {text_color};">
            <div style="font-weight: 700; color: {text_color}; margin-bottom: 0.8rem; font-size: 0.95rem;">
                🎯 Best for:
            </div>
            <div style="margin-left: 0.5rem; line-height: 1.7;">
                {best_for_html}
            </div>
        </div>
    </div>
</div>"""

        st.markdown(card_html, unsafe_allow_html=True)

        if st.button(button_text, key=button_key, use_container_width=True, type="primary"):
            self.session.set("selected_model_category", model_type)
            self.session.set("selected_model_type", model_type)  # Ensure both variables are set
            self.session.set("model_selection_stage", 2)
            st.rerun()

    def _render_unavailable_model_card(self) -> None:
        """Render card for unavailable ensemble models with responsive design."""
        # Calculate dynamic height for unavailable card
        min_height = 420  # Slightly larger for installation instructions

        # Create a clean, properly formatted HTML structure for unavailable card
        unavailable_card_html = f"""
<div style="
    padding: 1.8rem;
    border: 3px solid {COLORS['neutral_light']};
    border-radius: 15px;
    background: {COLORS['neutral_bg']};
    margin-bottom: 1.2rem;
    min-height: {min_height}px;
    height: auto;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    opacity: 0.7;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    font-family: 'Nunito Sans', sans-serif;
    word-wrap: break-word;
    overflow-wrap: break-word;
">
    <div style="text-align: center; margin-bottom: 1.5rem;">
        <div style="font-size: 3.2rem; margin-bottom: 0.8rem; line-height: 1; opacity: 0.6;">🌳</div>
        <div style="font-size: 1.5rem; font-weight: 700; color: {COLORS['neutral']}; margin-bottom: 0.6rem; line-height: 1.3;">Ensemble Models</div>
        <div style="font-size: 1.1rem; color: {COLORS['neutral']}; font-weight: 500; opacity: 0.8; line-height: 1.4;">Not Available</div>
    </div>
    <div style="flex-grow: 1; display: flex; flex-direction: column; gap: 1.2rem; font-size: 0.9rem; color: #2F4F4F; line-height: 1.6;">
        <div style="background: rgba(255, 255, 255, 0.4); padding: 1rem; border-radius: 10px; border-left: 4px solid {COLORS['warning']};">
            <div style="font-weight: 700; color: {COLORS['warning']}; margin-bottom: 0.8rem; font-size: 0.95rem;">
                📦 Requirements:
            </div>
            <div style="margin-left: 0.5rem; line-height: 1.7;">
                • XGBoost installation<br>
                • pip install xgboost<br>
                • Advanced ensemble methods<br>
                • Gradient boosting algorithms
            </div>
        </div>
        <div style="background: rgba(255, 255, 255, 0.4); padding: 1rem; border-radius: 10px; border-left: 4px solid {COLORS['info']};">
            <div style="font-weight: 700; color: {COLORS['info']}; margin-bottom: 0.8rem; font-size: 0.95rem;">
                💡 Installation:
            </div>
            <div style="margin-left: 0.5rem; line-height: 1.7;">
                • Run: pip install xgboost<br>
                • Restart application<br>
                • Access ensemble models<br>
                • Advanced ML capabilities
            </div>
        </div>
    </div>
</div>"""

        st.markdown(unavailable_card_html, unsafe_allow_html=True)

        st.button("🌳 Install XGBoost Required", key="install_xgboost_required",
                 use_container_width=True, disabled=True)

        st.info("💡 **Installation Required**: Run `pip install xgboost` to enable Ensemble Models")

    def _render_stage1_navigation(self) -> None:
        """Render Stage 1 navigation and current selection."""
        selected_category = self.session.get("selected_model_category", None)

        if selected_category:
            # Current selection display
            st.markdown("---")
            st.markdown(f"""
            <div style="
                padding: 1rem 1.5rem;
                background: {COLORS['success_bg']};
                border-left: 5px solid {COLORS['success']};
                border-radius: 8px;
                margin: 1rem 0;
                font-family: 'Nunito Sans', sans-serif;
                box-shadow: 0 2px 4px rgba(76, 175, 80, 0.1);
            ">
                <div style="
                    color: {COLORS['success']};
                    font-weight: 600;
                    font-size: 1.1rem;
                    line-height: 1.4;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                ">
                    <span style="font-size: 1.3rem;">✅</span>
                    <strong>Selected Model Category:</strong> {selected_category}
                </div>
            </div>
            """, unsafe_allow_html=True)

            # Navigation buttons
            col1, col2, col3 = st.columns([1, 2, 1])

            with col1:
                if st.button("🔄 Change Category", key="change_model_category", use_container_width=True):
                    self.session.set("selected_model_category", None)
                    st.rerun()

            with col3:
                if st.button("➡️ Proceed to Algorithm Selection", type="primary",
                           key="proceed_to_algorithm", use_container_width=True):
                    self.session.set("model_selection_stage", 2)
                    st.rerun()
        else:
            # No selection message
            st.markdown("---")
            st.markdown(f"""
            <div style="
                padding: 1rem 1.5rem;
                background: {COLORS['info_bg']};
                border-left: 5px solid {COLORS['info']};
                border-radius: 8px;
                margin: 1rem 0;
                font-family: 'Nunito Sans', sans-serif;
                box-shadow: 0 2px 4px rgba(33, 150, 243, 0.1);
            ">
                <div style="
                    color: {COLORS['info']};
                    font-weight: 600;
                    font-size: 1rem;
                    line-height: 1.4;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                ">
                    <span style="font-size: 1.2rem;">👆</span>
                    Please select a model category above to proceed to algorithm selection
                </div>
            </div>
            """, unsafe_allow_html=True)

    def _render_stage_header(self, stage_num: int, title: str, subtitle: str, icon: str,
                           help_topic: str, help_context: str, help_prompt: str) -> None:
        """Render a professional stage header with consistent styling."""
        # Main stage header with reduced spacing
        st.markdown(f"""
        <div style="
            background: {COLORS['primary_bg']};
            padding: 1.2rem 1.5rem;
            border-radius: 15px;
            margin-bottom: 0.8rem;
            border: 2px solid {COLORS['primary']};
            box-shadow: 0 4px 12px rgba(25, 118, 210, 0.15);
            font-family: 'Nunito Sans', sans-serif;
        ">
            <div style="display: flex; justify-content: space-between; align-items: flex-start;">
                <div style="flex: 1;">
                    <h2 style="
                        color: {COLORS['primary']};
                        margin: 0 0 0.3rem 0;
                        font-size: 1.8rem;
                        font-weight: 700;
                        display: flex;
                        align-items: center;
                        gap: 0.8rem;
                    ">
                        <span style="font-size: 2rem;">{icon}</span>
                        Stage {stage_num}: {title}
                    </h2>
                    <p style="
                        color: {COLORS['primary']};
                        margin: 0;
                        font-size: 1rem;
                        font-weight: 500;
                        opacity: 0.8;
                        line-height: 1.3;
                    ">
                        {subtitle}
                    </p>
                </div>
            </div>
        </div>
        """, unsafe_allow_html=True)

        # Help icon integrated with title
        help_icon_html = ChatGPTHelper.create_inline_help_icon(help_topic, help_context, help_prompt)
        st.markdown(f"""
        <div style="margin-bottom: 0.5rem;">
            {help_icon_html}
        </div>
        """, unsafe_allow_html=True)

    def _render_stage2_algorithm_selection(self) -> None:
        """Render Stage 2: Algorithm Selection within the chosen model type."""
        selected_type = self.session.get("selected_model_type", None)

        if not selected_type:
            st.error("❌ No model type selected. Please return to Stage 1.")
            if st.button("🔙 Back to Model Type Selection", key="back_to_stage1"):
                self.session.set("model_selection_stage", 1)
                st.rerun()
            return

        col1, col2 = st.columns([4, 1])
        with col1:
            st.markdown(f"### 🤖 Stage 2: Algorithm Selection - {selected_type}")
            st.markdown("*Choose the specific algorithm within your selected model type*")
        with col2:
            help_icon_html = ChatGPTHelper.create_inline_help_icon(
                f"{selected_type} Algorithm Selection",
                f"understanding algorithms in {selected_type.lower()}",
                f"Please explain the different algorithms available in {selected_type} for chemometric analysis. What are the specific characteristics, advantages, and use cases of each algorithm? How should I choose between them for spectroscopic data analysis?"
            )
            st.markdown(help_icon_html, unsafe_allow_html=True)

        # Enhanced selected model type display
        st.markdown(f"""
<div style="
    padding: 1rem 1.5rem;
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    border-left: 5px solid #4CAF50;
    border-radius: 8px;
    margin: 1rem 0;
    font-family: 'Nunito Sans', sans-serif;
    box-shadow: 0 2px 4px rgba(76, 175, 80, 0.1);
">
    <div style="
        color: #2E7D32;
        font-weight: 600;
        font-size: 1rem;
        line-height: 1.4;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    ">
        <span style="font-size: 1.2rem;">🎯</span>
        <strong>Selected Model Type:</strong> {selected_type}
    </div>
</div>""", unsafe_allow_html=True)

        # Algorithm selection based on model type
        if selected_type == "PLS Models":
            self._render_pls_algorithm_selection()
        elif selected_type == "Neural Network Models":
            self._render_neural_network_algorithm_selection()
        elif selected_type == "Support Vector Models":
            self._render_support_vector_algorithm_selection()
        elif selected_type == "Ensemble Models":
            self._render_ensemble_algorithm_selection()

        # Navigation buttons
        st.markdown("---")
        col1, col2, col3 = st.columns([1, 2, 1])

        with col1:
            if st.button("🔙 Back to Model Type", key="back_to_model_type"):
                self.session.set("model_selection_stage", 1)
                st.rerun()

        with col3:
            selected_algorithm = self.session.get("selected_algorithm", None)
            if selected_algorithm:
                if st.button("➡️ Proceed to Cross-Validation", type="primary", key="proceed_to_cv"):
                    self.session.set("model_selection_stage", 3)
                    st.rerun()
            else:
                st.button("➡️ Select Algorithm First", key="select_algorithm_first", disabled=True)

    def _render_stage3_cross_validation_configuration(self, x_train: pd.DataFrame, y_train: pd.DataFrame) -> None:
        """Render Stage 3: Cross-Validation Configuration."""
        if not self.session.has("selected_algorithm"):
            st.error("❌ No algorithm selected. Please complete Stage 2 first.")
            if st.button("🔙 Back to Algorithm Selection", key="back_to_stage2"):
                self.session.set("model_selection_stage", 2)
                st.rerun()
            return

        selected_algorithm = self.session.get("selected_algorithm")

        col1, col2 = st.columns([4, 1])
        with col1:
            st.markdown("### 🔄 Stage 3: Cross-Validation Configuration")
            st.markdown("*Configure cross-validation method and parameters for robust model evaluation*")
        with col2:
            ChatGPTHelper.create_help_icon(
                "Cross-Validation in Chemometrics",
                "understanding cross-validation methods for chemometric model evaluation",
                f"""Please explain cross-validation methods for chemometric model evaluation:

**Selected Algorithm**: {selected_algorithm}
**Dataset Information**:
- Training samples: {x_train.shape[0]}
- Features: {x_train.shape[1]}
- Response variables: {y_train.shape[1] if len(y_train.shape) > 1 else 1}

Please help me understand:
1. What cross-validation methods are most suitable for chemometric data?
2. How should I choose the number of folds for my dataset size?
3. What are the advantages and disadvantages of different CV methods?
4. How does cross-validation help in model selection and validation?
5. What should I consider when choosing CV parameters for {selected_algorithm}?
6. How do I interpret cross-validation results in chemometrics?

Please provide practical guidance for selecting and configuring cross-validation for chemometric analysis."""
            )

        # Enhanced selected algorithm display
        st.markdown(f"""
<div style="
    padding: 1rem 1.5rem;
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    border-left: 5px solid #4CAF50;
    border-radius: 8px;
    margin: 1rem 0;
    font-family: 'Nunito Sans', sans-serif;
    box-shadow: 0 2px 4px rgba(76, 175, 80, 0.1);
">
    <div style="
        color: #2E7D32;
        font-weight: 600;
        font-size: 1rem;
        line-height: 1.4;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    ">
        <span style="font-size: 1.2rem;">🎯</span>
        <strong>Selected Algorithm:</strong> {selected_algorithm}
    </div>
</div>""", unsafe_allow_html=True)

        # Cross-validation method selection
        st.markdown("#### 🔄 Cross-Validation Method Selection")

        # Card-based CV selection approach (similar to Stage 4)
        st.markdown("##### 🎯 Choose Your CV Configuration Approach")
        st.markdown("Select how you want to configure your cross-validation method:")

        # Create two columns for the cards
        col1, col2 = st.columns(2, gap="large")

        with col1:
            # Automatic Selection Card
            automatic_card = self._create_route_card(
                title="Automatic Selection",
                subtitle="Recommended Approach",
                icon="🤖",
                features=[
                    "Analyzes your data characteristics",
                    "Recommends optimal CV method",
                    "Configures parameters automatically",
                    "Based on best practices",
                    "Quick and reliable setup"
                ],
                border_color=COLORS['primary'],
                bg_gradient=COLORS['primary_bg'],
                text_color=COLORS['primary']
            )
            st.markdown(automatic_card, unsafe_allow_html=True)

            if st.button("🤖 Choose Automatic Selection", key="choose_automatic_cv", use_container_width=True):
                self.session.set("cv_selection_mode", "automatic")
                st.rerun()

        with col2:
            # Manual Configuration Card
            manual_card = self._create_route_card(
                title="Manual Configuration",
                subtitle="Expert Control",
                icon="⚙️",
                features=[
                    "Choose specific CV method",
                    "Configure all parameters manually",
                    "Full control over validation",
                    "Expert-level customization",
                    "Detailed method information"
                ],
                border_color=COLORS['warning'],
                bg_gradient=COLORS['warning_bg'],
                text_color=COLORS['warning']
            )
            st.markdown(manual_card, unsafe_allow_html=True)

            if st.button("⚙️ Choose Manual Configuration", key="choose_manual_cv", use_container_width=True):
                self.session.set("cv_selection_mode", "manual")
                st.rerun()

        # Render the selected approach
        cv_selection_mode = self.session.get("cv_selection_mode", None)

        if cv_selection_mode == "automatic":
            st.markdown("---")
            # Show current selection with change option
            st.markdown(f"""
<div style="
    padding: 1rem 1.5rem;
    background: {COLORS['primary_bg']};
    border-left: 5px solid {COLORS['primary']};
    border-radius: 8px;
    margin: 1rem 0;
    font-family: 'Nunito Sans', sans-serif;
    box-shadow: 0 2px 4px rgba(25, 118, 210, 0.1);
">
    <div style="
        color: {COLORS['primary']};
        font-weight: 600;
        font-size: 1rem;
        line-height: 1.4;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    ">
        <span style="font-size: 1.2rem;">🤖</span>
        <strong>Selected Approach:</strong> Automatic Selection
    </div>
</div>""", unsafe_allow_html=True)

            # Change approach button
            col1, _, _ = st.columns([1, 2, 1])
            with col1:
                if st.button("🔄 Change Approach", key="change_cv_approach_from_auto"):
                    self.session.set("cv_selection_mode", None)
                    st.rerun()

            self._render_automatic_cv_selection(x_train, y_train, selected_algorithm)

        elif cv_selection_mode == "manual":
            st.markdown("---")
            # Show current selection with change option
            st.markdown(f"""
<div style="
    padding: 1rem 1.5rem;
    background: {COLORS['warning_bg']};
    border-left: 5px solid {COLORS['warning']};
    border-radius: 8px;
    margin: 1rem 0;
    font-family: 'Nunito Sans', sans-serif;
    box-shadow: 0 2px 4px rgba(255, 152, 0, 0.1);
">
    <div style="
        color: {COLORS['warning']};
        font-weight: 600;
        font-size: 1rem;
        line-height: 1.4;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    ">
        <span style="font-size: 1.2rem;">⚙️</span>
        <strong>Selected Approach:</strong> Manual Configuration
    </div>
</div>""", unsafe_allow_html=True)

            # Change approach button
            col1, _, _ = st.columns([1, 2, 1])
            with col1:
                if st.button("🔄 Change Approach", key="change_cv_approach_from_manual"):
                    self.session.set("cv_selection_mode", None)
                    st.rerun()

            self._render_manual_cv_selection(x_train, y_train, selected_algorithm)

        # Navigation buttons
        st.markdown("---")
        col1, col2, col3 = st.columns([1, 2, 1])

        with col1:
            if st.button("🔙 Back to Algorithm", key="back_to_algorithm_from_cv"):
                self.session.set("model_selection_stage", 2)
                st.rerun()

        with col3:
            if self.session.has("cv_configuration"):
                if st.button("➡️ Proceed to Optimization", type="primary", key="proceed_to_optimization_from_cv"):
                    self.session.set("model_selection_stage", 4)
                    st.rerun()
            else:
                st.button("➡️ Configure CV First", key="configure_cv_first", disabled=True)

    def _render_automatic_cv_selection(self, x_train: pd.DataFrame, y_train: pd.DataFrame, algorithm: str) -> None:
        """Render automatic cross-validation selection interface."""
        st.markdown("##### 🤖 Automatic Cross-Validation Selection")
        # Enhanced automatic selection info box
        st.markdown("""
<div style="
    padding: 1rem 1.5rem;
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-left: 5px solid #1976D2;
    border-radius: 8px;
    margin: 1rem 0;
    font-family: 'Nunito Sans', sans-serif;
    box-shadow: 0 2px 4px rgba(25, 118, 210, 0.1);
">
    <div style="
        color: #1565C0;
        font-weight: 600;
        font-size: 1rem;
        line-height: 1.4;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    ">
        <span style="font-size: 1.2rem;">🔍</span>
        <strong>Automatic Selection:</strong> Analyzes your data characteristics to recommend optimal CV method
    </div>
</div>""", unsafe_allow_html=True)

        # Analyze data characteristics
        n_samples = x_train.shape[0]
        n_features = x_train.shape[1]
        n_responses = y_train.shape[1] if len(y_train.shape) > 1 else 1

        # Data analysis
        st.markdown("**📊 Data Analysis:**")
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Samples", n_samples)
        with col2:
            st.metric("Features", n_features)
        with col3:
            st.metric("Responses", n_responses)

        # Automatic recommendation logic
        recommended_cv = self._get_recommended_cv_method(n_samples, n_features, algorithm)

        st.markdown("**🎯 Recommended Cross-Validation Configuration:**")

        # Display recommendation with reasoning
        cv_method = recommended_cv["method"]
        cv_params = recommended_cv["params"]
        reasoning = recommended_cv["reasoning"]

        st.success(f"✅ **Recommended Method**: {cv_method}")
        st.info(f"**Reasoning**: {reasoning}")



        # Parameter configuration
        st.markdown("**⚙️ Recommended Parameters:**")
        for param, value in cv_params.items():
            st.write(f"• **{param}**: {value}")

        # Apply automatic configuration
        if st.button("✅ Apply Automatic Configuration", type="primary", key="apply_auto_cv"):
            cv_config = {
                "mode": "automatic",
                "method": cv_method,
                "params": cv_params,
                "reasoning": reasoning,
                "data_characteristics": {
                    "n_samples": n_samples,
                    "n_features": n_features,
                    "n_responses": n_responses
                }
            }
            self.session.set("cv_configuration", cv_config)
            st.success(f"✅ Automatic CV configuration applied: {cv_method}")
            st.rerun()

    def _render_manual_cv_selection(self, x_train: pd.DataFrame, y_train: pd.DataFrame, algorithm: str) -> None:
        """Render manual cross-validation selection interface."""
        st.markdown("##### ⚙️ Manual Cross-Validation Configuration")
        # Enhanced manual configuration info box
        st.markdown("""
<div style="
    padding: 1rem 1.5rem;
    background: linear-gradient(135deg, #fff3e0 0%, #ffcc80 100%);
    border-left: 5px solid #FF9800;
    border-radius: 8px;
    margin: 1rem 0;
    font-family: 'Nunito Sans', sans-serif;
    box-shadow: 0 2px 4px rgba(255, 152, 0, 0.1);
">
    <div style="
        color: #E65100;
        font-weight: 600;
        font-size: 1rem;
        line-height: 1.4;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    ">
        <span style="font-size: 1.2rem;">🔧</span>
        <strong>Manual Configuration:</strong> Choose specific CV method and parameters based on your requirements
    </div>
</div>""", unsafe_allow_html=True)

        # CV method selection
        cv_method = st.selectbox(
            "Select Cross-Validation Method:",
            list(CV_METHODS.keys()),
            help="Choose the cross-validation method that best fits your analysis needs"
        )

        # Show method information
        if cv_method in CV_METHODS:
            method_info = CV_METHODS[cv_method]

            st.markdown(f"**📋 {cv_method}**")
            st.write(f"**Description**: {method_info['description']}")
            st.write(f"**Suitable for**: {method_info['suitable_for']}")

            # Parameter configuration
            st.markdown("**⚙️ Method Parameters:**")
            cv_params = self._render_cv_parameters(cv_method, x_train, y_train)

            # Validation and warnings
            self._render_cv_validation_warnings(cv_method, cv_params, x_train.shape[0])

            # Apply manual configuration
            if st.button("✅ Apply Manual Configuration", type="primary", key="apply_manual_cv"):
                cv_config = {
                    "mode": "manual",
                    "method": cv_method,
                    "params": cv_params,
                    "method_info": method_info
                }
                self.session.set("cv_configuration", cv_config)
                st.success(f"✅ Manual CV configuration applied: {cv_method}")
                st.rerun()

    def _get_recommended_cv_method(self, n_samples: int, n_features: int, algorithm: str) -> Dict[str, Any]:
        """Get recommended cross-validation method based on data characteristics."""

        # Decision logic based on sample size and algorithm
        if n_samples < 20:
            # Very small dataset
            return {
                "method": "Leave-One-Out",
                "params": {},
                "reasoning": f"With only {n_samples} samples, LOOCV maximizes training data usage while providing robust validation."
            }
        elif n_samples < 50:
            # Small dataset
            return {
                "method": "K-Fold",
                "params": {"n_splits": min(5, n_samples//2)},
                "reasoning": f"For {n_samples} samples, {min(5, n_samples//2)}-fold CV provides good balance between training data and validation reliability."
            }
        elif n_samples < 100:
            # Medium dataset
            if "PLS" in algorithm:
                return {
                    "method": "K-Fold",
                    "params": {"n_splits": 5},
                    "reasoning": f"5-fold CV is standard for PLS with {n_samples} samples, providing reliable RMSECV estimates."
                }
            else:
                return {
                    "method": "Repeated K-Fold",
                    "params": {"n_splits": 5, "n_repeats": 3},
                    "reasoning": f"Repeated 5-fold CV provides more robust estimates for {algorithm} with {n_samples} samples."
                }
        else:
            # Large dataset
            if "Neural Network" in algorithm or "XGBoost" in algorithm:
                return {
                    "method": "K-Fold",
                    "params": {"n_splits": 10},
                    "reasoning": f"10-fold CV is efficient for {algorithm} with {n_samples} samples, balancing computational cost and validation quality."
                }
            else:
                return {
                    "method": "Shuffle Split",
                    "params": {"n_splits": 10, "test_size": 0.2},
                    "reasoning": f"Shuffle Split is efficient for large datasets ({n_samples} samples) while maintaining validation quality."
                }

    def _render_cv_parameters(self, cv_method: str, x_train: pd.DataFrame, y_train: pd.DataFrame) -> Dict[str, Any]:
        """Render cross-validation parameter controls with comprehensive options."""
        params = {}
        n_samples = x_train.shape[0]

        if cv_method == "K-Fold":
            col1, col2, col3 = st.columns(3)
            with col1:
                params['n_splits'] = st.slider(
                    "Number of Folds",
                    min_value=3,
                    max_value=min(10, n_samples//2),
                    value=5,
                    help="Number of folds for K-Fold cross-validation"
                )
            with col2:
                params['shuffle'] = st.checkbox(
                    "Shuffle Data",
                    value=True,
                    help="Randomly shuffle data before splitting"
                )
            with col3:
                if params['shuffle']:
                    params['random_state'] = st.number_input(
                        "Random State",
                        value=42,
                        help="Random seed for reproducibility"
                    )

        elif cv_method == "Stratified K-Fold":
            col1, col2, col3 = st.columns(3)
            with col1:
                params['n_splits'] = st.slider(
                    "Number of Folds",
                    min_value=3,
                    max_value=min(10, n_samples//2),
                    value=5,
                    help="Number of folds for Stratified K-Fold cross-validation"
                )
            with col2:
                params['shuffle'] = st.checkbox(
                    "Shuffle Data",
                    value=True,
                    help="Randomly shuffle data before splitting"
                )
            with col3:
                if params['shuffle']:
                    params['random_state'] = st.number_input(
                        "Random State",
                        value=42,
                        help="Random seed for reproducibility"
                    )

        elif cv_method == "Leave-One-Out":
            st.info("ℹ️ Leave-One-Out CV has no configurable parameters - uses n-1 samples for training in each fold.")

        elif cv_method == "Leave-P-Out":
            max_p = min(5, n_samples//3)
            col1, col2 = st.columns(2)
            with col1:
                params['p'] = st.slider(
                    "P (samples to leave out)",
                    min_value=1,
                    max_value=max_p,
                    value=min(2, max_p),
                    help="Number of samples to leave out in each iteration"
                )
            with col2:
                # Calculate and display number of combinations
                from math import comb
                n_combinations = comb(n_samples, params['p'])
                st.metric("Total Combinations", f"{n_combinations:,}")
                if n_combinations > 1000:
                    st.warning("⚠️ Very high number of combinations - consider reducing P or using different CV method")

        elif cv_method == "Repeated K-Fold":
            col1, col2, col3 = st.columns(3)
            with col1:
                params['n_splits'] = st.slider(
                    "Number of Folds",
                    min_value=3,
                    max_value=min(10, n_samples//2),
                    value=5,
                    help="Number of folds for each repetition"
                )
            with col2:
                params['n_repeats'] = st.slider(
                    "Number of Repetitions",
                    min_value=2,
                    max_value=10,
                    value=3,
                    help="Number of times to repeat the cross-validation"
                )
            with col3:
                params['random_state'] = st.number_input(
                    "Random State",
                    value=42,
                    help="Random seed for reproducibility"
                )

            # Show total number of model fits
            total_fits = params['n_splits'] * params['n_repeats']
            st.info(f"ℹ️ Total model fits: {total_fits}")

        elif cv_method == "Shuffle Split":
            col1, col2, col3 = st.columns(3)
            with col1:
                params['n_splits'] = st.slider(
                    "Number of Splits",
                    min_value=5,
                    max_value=50,
                    value=10,
                    help="Number of re-shuffling & splitting iterations"
                )
            with col2:
                params['test_size'] = st.slider(
                    "Test Size",
                    min_value=0.1,
                    max_value=0.5,
                    value=0.2,
                    step=0.05,
                    help="Proportion of dataset to include in test split"
                )
            with col3:
                params['train_size'] = st.slider(
                    "Train Size",
                    min_value=0.5,
                    max_value=0.9,
                    value=1.0 - params['test_size'],
                    step=0.05,
                    help="Proportion of dataset to include in train split"
                )

            params['random_state'] = st.number_input("Random State", value=42, help="Random seed for reproducibility")

        elif cv_method == "Time Series Split":
            col1, col2, col3 = st.columns(3)
            with col1:
                params['n_splits'] = st.slider(
                    "Number of Splits",
                    min_value=3,
                    max_value=min(10, n_samples//3),
                    value=5,
                    help="Number of splits for time series cross-validation"
                )
            with col2:
                params['max_train_size'] = st.number_input(
                    "Max Train Size",
                    min_value=0,
                    max_value=n_samples,
                    value=0,
                    help="Maximum number of training samples (0 = no limit)"
                )
                if params['max_train_size'] == 0:
                    params['max_train_size'] = None
            with col3:
                params['test_size'] = st.number_input(
                    "Test Size",
                    min_value=1,
                    max_value=n_samples//3,
                    value=n_samples//10,
                    help="Number of samples in each test set"
                )

            params['gap'] = st.number_input(
                "Gap Size",
                min_value=0,
                max_value=n_samples//5,
                value=0,
                help="Number of samples to exclude between train and test"
            )

        elif cv_method == "Group K-Fold":
            col1, col2 = st.columns(2)
            with col1:
                params['n_splits'] = st.slider(
                    "Number of Folds",
                    min_value=3,
                    max_value=min(10, n_samples//2),
                    value=5,
                    help="Number of folds for Group K-Fold cross-validation"
                )
            with col2:
                st.warning("⚠️ **Group Information Required**")
                st.write("Group K-Fold requires group labels for each sample.")
                st.write("Ensure your data has appropriate grouping variables.")

        elif cv_method == "Monte Carlo Cross-Validation":
            col1, col2, col3 = st.columns(3)
            with col1:
                params['n_splits'] = st.slider(
                    "Number of Splits",
                    min_value=50,
                    max_value=500,
                    value=100,
                    step=10,
                    help="Number of random splits (more = smoother estimates)"
                )
            with col2:
                params['test_size'] = st.slider(
                    "Test Size",
                    min_value=0.1,
                    max_value=0.4,
                    value=0.2,
                    step=0.05,
                    help="Proportion of dataset for testing"
                )
            with col3:
                params['random_state'] = st.number_input(
                    "Random State",
                    value=42,
                    help="Random seed for reproducibility"
                )

            st.info(f"ℹ️ Will perform {params['n_splits']} random train/test splits")

        elif cv_method == "Nested Cross-Validation":
            col1, col2 = st.columns(2)
            with col1:
                params['outer_cv'] = st.slider(
                    "Outer CV Folds",
                    min_value=3,
                    max_value=10,
                    value=5,
                    help="Number of folds for outer cross-validation (performance estimation)"
                )
            with col2:
                params['inner_cv'] = st.slider(
                    "Inner CV Folds",
                    min_value=3,
                    max_value=8,
                    value=3,
                    help="Number of folds for inner cross-validation (model selection)"
                )

            total_fits = params['outer_cv'] * params['inner_cv']
            st.warning(f"⚠️ **Computational Warning**: Nested CV will perform {total_fits} model fits")
            st.info("ℹ️ Nested CV provides unbiased performance estimates but is computationally expensive")

        return params

    def _render_cv_validation_warnings(self, cv_method: str, cv_params: Dict[str, Any], n_samples: int) -> None:
        """Render validation warnings and recommendations for CV configuration."""

        if cv_method == "Leave-One-Out" and n_samples > 200:
            st.warning("⚠️ **Performance Warning**: Leave-One-Out CV with large datasets (>200 samples) can be very slow. Consider K-Fold CV instead.")

        elif cv_method == "Leave-P-Out":
            p = cv_params.get('p', 2)
            from math import comb
            n_combinations = comb(n_samples, p) if n_samples >= p else 0
            if n_combinations > 1000:
                st.error(f"❌ **Too Many Combinations**: Leave-{p}-Out will create {n_combinations:,} combinations. This is computationally prohibitive.")
                st.info("💡 **Recommendation**: Reduce P value or use a different CV method.")
            elif n_combinations > 100:
                st.warning(f"⚠️ **High Computational Cost**: Leave-{p}-Out will create {n_combinations:,} combinations. This may take significant time.")

        elif cv_method == "Repeated K-Fold":
            n_splits = cv_params.get('n_splits', 5)
            n_repeats = cv_params.get('n_repeats', 3)
            total_fits = n_splits * n_repeats
            if total_fits > 50:
                st.warning(f"⚠️ **High Computational Cost**: {total_fits} total model fits will be performed. This may take significant time.")

        elif cv_method == "Monte Carlo Cross-Validation":
            n_splits = cv_params.get('n_splits', 100)
            if n_splits > 200:
                st.warning(f"⚠️ **High Computational Cost**: {n_splits} random splits will be performed. Consider reducing the number of splits.")

        elif cv_method == "Nested Cross-Validation":
            outer_cv = cv_params.get('outer_cv', 5)
            inner_cv = cv_params.get('inner_cv', 3)
            total_fits = outer_cv * inner_cv
            st.error(f"❌ **Very High Computational Cost**: Nested CV will perform {total_fits} model fits. This is extremely time-consuming.")
            st.info("💡 **Recommendation**: Use Nested CV only for final model validation or research purposes.")

        elif cv_method == "Time Series Split" and n_samples < 50:
            st.warning("⚠️ **Small Dataset Warning**: Time Series Split with small datasets may result in very small training sets for early splits.")

        # General recommendations
        if n_samples < 30:
            st.info("💡 **Small Dataset Recommendation**: Consider Leave-One-Out or Leave-P-Out CV for maximum data utilization.")
        elif n_samples > 1000:
            st.info("💡 **Large Dataset Recommendation**: K-Fold or Shuffle Split CV are efficient choices for large datasets.")




    def _render_pls_algorithm_selection(self) -> None:
        """Render PLS algorithm selection."""
        st.markdown("#### 📊 Available PLS Algorithms")

        col1, col2 = st.columns(2)

        with col1:
            # NIPALS Algorithm
            nipals_card = self._create_model_card(
                title="NIPALS (PLS1, PLS2)",
                subtitle="Nonlinear Iterative Partial Least Squares",
                icon="🔄",
                characteristics=[
                    "Authentic NIPALS implementation",
                    "Iterative deflation algorithm",
                    "Handles missing data well",
                    "Supports both PLS1 and PLS2",
                    "Chemometric standard"
                ],
                best_for=[
                    "Traditional chemometric analysis",
                    "Data with missing values",
                    "Multiple response variables (PLS2)",
                    "Regulatory compliance"
                ],
                border_color="#2E8B57",
                bg_gradient="linear-gradient(135deg, #f0fff0 0%, #e6ffe6 100%)",
                text_color="#2E8B57"
            )
            st.markdown(nipals_card, unsafe_allow_html=True)

            if st.button("🔄 Select NIPALS (PLS1, PLS2)", key="select_nipals", use_container_width=True):
                self.session.set("selected_algorithm", "NIPALS (PLS1, PLS2)")
                st.success("✅ Selected: NIPALS (PLS1, PLS2)")
                st.rerun()

        with col2:
            # SIMPLS Algorithm
            simpls_card = self._create_model_card(
                title="SIMPLS",
                subtitle="Simple Partial Least Squares",
                icon="⚡",
                characteristics=[
                    "Computationally efficient",
                    "Good orthogonality properties",
                    "Faster than NIPALS for large datasets",
                    "Direct algorithm",
                    "Stable numerical properties"
                ],
                best_for=[
                    "Large datasets",
                    "When computational speed is important",
                    "Well-conditioned data matrices"
                ],
                border_color="#4169E1",
                bg_gradient="linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%)",
                text_color="#4169E1"
            )
            st.markdown(simpls_card, unsafe_allow_html=True)

            if st.button("⚡ Select SIMPLS", key="select_simpls", use_container_width=True):
                self.session.set("selected_algorithm", "SIMPLS")
                st.success("✅ Selected: SIMPLS")
                st.rerun()

        # Show current selection
        selected_algorithm = self.session.get("selected_algorithm", None)
        if selected_algorithm and selected_algorithm in ["NIPALS (PLS1, PLS2)", "SIMPLS"]:
            st.markdown("---")
            st.success(f"✅ **Selected Algorithm**: {selected_algorithm}")

            if st.button("🔄 Change Algorithm", key="change_pls_algorithm"):
                self.session.set("selected_algorithm", None)
                st.rerun()

    def _render_neural_network_algorithm_selection(self) -> None:
        """Render Neural Network algorithm selection."""
        st.markdown("#### 🧠 Available Neural Network Algorithms")

        # Create two columns for the two neural network options
        col1, col2 = st.columns(2)

        with col1:
            # Enhanced ANN Algorithm (MATLAB-style)
            enhanced_ann_card = self._create_model_card(
                title="Enhanced ANN (MATLAB-style)",
                subtitle="Chemometric Neural Network",
                icon="🎯",
                characteristics=[
                    "MATLAB-style preprocessing",
                    "PCA integration",
                    "Custom data division",
                    "Proper normalization (mapstd)",
                    "Comprehensive evaluation"
                ],
                best_for=[
                    "Chemometric applications",
                    "Spectroscopic data analysis",
                    "MATLAB workflow compatibility",
                    "Research and validation"
                ],
                border_color="#FF6B35",
                bg_gradient="linear-gradient(135deg, #fff5f0 0%, #ffe6d9 100%)",
                text_color="#FF6B35"
            )
            st.markdown(enhanced_ann_card, unsafe_allow_html=True)

            if st.button("🎯 Select Enhanced ANN (MATLAB-style)", key="select_enhanced_ann", use_container_width=True):
                self.session.set("selected_algorithm", "Enhanced ANN (MATLAB-style)")
                st.success("✅ Selected: Enhanced ANN (MATLAB-style)")
                st.rerun()

        with col2:
            # MLP Algorithm
            mlp_card = self._create_model_card(
                title="Multilayer Perceptron (MLP)",
                subtitle="Standard Neural Network",
                icon="🧠",
                characteristics=[
                    "Multiple hidden layers",
                    "Various activation functions",
                    "Advanced optimization solvers",
                    "Regularization capabilities",
                    "Excellent for non-linear patterns"
                ],
                best_for=[
                    "Complex non-linear relationships",
                    "Large datasets",
                    "High predictive accuracy requirements"
                ],
                border_color="#4169E1",
                bg_gradient="linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%)",
                text_color="#4169E1"
            )
            st.markdown(mlp_card, unsafe_allow_html=True)

            if st.button("🧠 Select Multilayer Perceptron (MLP)", key="select_mlp", use_container_width=True):
                self.session.set("selected_algorithm", "Multilayer Perceptron (MLP)")
                st.success("✅ Selected: Multilayer Perceptron (MLP)")
                st.rerun()



        # Show current selection
        selected_algorithm = self.session.get("selected_algorithm", None)
        if selected_algorithm and selected_algorithm in ["Enhanced ANN (MATLAB-style)", "Multilayer Perceptron (MLP)"]:
            st.markdown("---")
            st.success(f"✅ **Selected Algorithm**: {selected_algorithm}")

            if st.button("🔄 Change Algorithm", key="change_neural_algorithm"):
                self.session.set("selected_algorithm", None)
                st.rerun()

    def _render_support_vector_algorithm_selection(self) -> None:
        """Render Support Vector algorithm selection."""
        st.markdown("#### ⚡ Available Support Vector Algorithms")

        col1, col2 = st.columns(2)

        with col1:
            # ε-SVR Algorithm
            svr_card = self._create_model_card(
                title="ε-Support Vector Regression",
                subtitle="Epsilon-SVR",
                icon="⚡",
                characteristics=[
                    "Most standard SVR approach",
                    "Epsilon-insensitive loss function",
                    "Multiple kernel options",
                    "Robust to outliers",
                    "Good generalization capability"
                ],
                best_for=[
                    "High-dimensional data",
                    "Non-linear relationships (with kernels)",
                    "Robust regression requirements"
                ],
                border_color="#FF6B35",
                bg_gradient="linear-gradient(135deg, #fff5f0 0%, #ffe6d9 100%)",
                text_color="#FF6B35"
            )
            st.markdown(svr_card, unsafe_allow_html=True)

            if st.button("⚡ Select ε-Support Vector Regression", key="select_svr", use_container_width=True):
                self.session.set("selected_algorithm", "ε-Support Vector Regression (ε-SVR)")
                st.success("✅ Selected: ε-Support Vector Regression (ε-SVR)")
                st.rerun()

        with col2:
            # Nu-SVR Algorithm
            nusvr_card = self._create_model_card(
                title="Nu-Support Vector Regression",
                subtitle="Nu-SVR",
                icon="🎯",
                characteristics=[
                    "Alternative to ε-SVR",
                    "Uses ν parameter instead of ε",
                    "Automatic support vector selection",
                    "Good for noisy data",
                    "Flexible error control"
                ],
                best_for=[
                    "When automatic parameter selection is preferred",
                    "Noisy datasets",
                    "When you want to control fraction of support vectors"
                ],
                border_color="#E91E63",
                bg_gradient="linear-gradient(135deg, #fce4ec 0%, #f8bbd9 100%)",
                text_color="#E91E63"
            )
            st.markdown(nusvr_card, unsafe_allow_html=True)

            if st.button("🎯 Select Nu-Support Vector Regression", key="select_nusvr", use_container_width=True):
                self.session.set("selected_algorithm", "Nu-Support Vector Regression (Nu-SVR)")
                st.success("✅ Selected: Nu-Support Vector Regression (Nu-SVR)")
                st.rerun()

        # Show current selection
        selected_algorithm = self.session.get("selected_algorithm", None)
        if selected_algorithm and selected_algorithm in ["ε-Support Vector Regression (ε-SVR)", "Nu-Support Vector Regression (Nu-SVR)"]:
            st.markdown("---")
            st.success(f"✅ **Selected Algorithm**: {selected_algorithm}")

            if st.button("🔄 Change Algorithm", key="change_svm_algorithm"):
                self.session.set("selected_algorithm", None)
                st.rerun()

    def _render_ensemble_algorithm_selection(self) -> None:
        """Render Ensemble algorithm selection."""
        st.markdown("#### 🌳 Available Ensemble Algorithms")

        if not HAS_XGBOOST:
            st.error("❌ XGBoost is not installed. Please install it to use Ensemble Models.")
            st.code("pip install xgboost")
            return

        # XGBoost Algorithm (centered single card)
        col1, col2, col3 = st.columns([1, 2, 1])

        with col2:
            xgboost_card = self._create_model_card(
                title="XGBoost",
                subtitle="Extreme Gradient Boosting",
                icon="🌳",
                characteristics=[
                    "Gradient boosting ensemble method",
                    "Excellent predictive performance",
                    "Feature importance ranking",
                    "Handles missing values automatically",
                    "Regularization capabilities",
                    "Parallel processing support"
                ],
                best_for=[
                    "Maximum predictive accuracy",
                    "Feature importance analysis",
                    "Complex non-linear relationships",
                    "Large datasets with mixed data types"
                ],
                border_color="#9C27B0",
                bg_gradient="linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%)",
                text_color="#9C27B0"
            )
            st.markdown(xgboost_card, unsafe_allow_html=True)

            if st.button("🌳 Select XGBoost", key="select_xgboost", use_container_width=True):
                self.session.set("selected_algorithm", "XGBoost")
                st.success("✅ Selected: XGBoost")
                st.rerun()

        # Show current selection
        selected_algorithm = self.session.get("selected_algorithm", None)
        if selected_algorithm and selected_algorithm == "XGBoost":
            st.markdown("---")
            st.success(f"✅ **Selected Algorithm**: {selected_algorithm}")

            if st.button("🔄 Change Algorithm", key="change_ensemble_algorithm"):
                self.session.set("selected_algorithm", None)
                st.rerun()

    def _render_stage4_optimization_route_selection(self, x_train: pd.DataFrame, y_train: pd.DataFrame) -> None:
        """Render Stage 4: Optimization Route Selection."""
        selected_algorithm = self.session.get("selected_algorithm", None)
        cv_configuration = self.session.get("cv_configuration", None)

        if not selected_algorithm:
            st.error("❌ No algorithm selected. Please return to Stage 2.")
            if st.button("🔙 Back to Algorithm Selection", key="back_to_stage2"):
                self.session.set("model_selection_stage", 2)
                st.rerun()
            return

        if not cv_configuration:
            st.error("❌ No cross-validation configuration found. Please complete Stage 3 first.")
            if st.button("🔙 Back to Cross-Validation", key="back_to_stage3"):
                self.session.set("model_selection_stage", 3)
                st.rerun()
            return

        col1, col2 = st.columns([4, 1])
        with col1:
            st.markdown(f"### ⚙️ Stage 4: Optimization Route Selection")
            st.markdown(f"*Choose optimization approach for {selected_algorithm}*")
        with col2:
            ChatGPTHelper.create_help_icon(
                f"{selected_algorithm} Optimization",
                f"understanding optimization approaches for {selected_algorithm.lower()}",
                f"Please explain the different optimization approaches for {selected_algorithm} in chemometrics: manual parameter configuration vs automated grid search optimization. What are the advantages of each approach and when should I use manual vs automated optimization for spectroscopic data analysis?"
            )

        st.info(f"🎯 **Selected Algorithm**: {selected_algorithm}")

        # Optimization route selection
        selected_route = self.session.get("selected_optimization_route", None)

        if selected_route is None:
            self._render_optimization_route_selection()
        else:
            # Show selected route and allow change
            st.markdown("#### 🛤️ Selected Optimization Route")

            col1, col2 = st.columns([3, 1])
            with col1:
                route_name = "🧠 Manual Parameter Configuration" if selected_route == "manual" else "🤖 Automated Grid Search"
                st.info(f"**Current Route**: {route_name}")
            with col2:
                if st.button("🔄 Change Route", key="change_optimization_route"):
                    self.session.set("selected_optimization_route", None)
                    st.rerun()

            # Render route-specific interface
            if selected_route == "manual":
                self._render_manual_optimization_interface(x_train, y_train, selected_algorithm)
            elif selected_route == "automatic":
                self._render_automatic_optimization_interface(x_train, y_train, selected_algorithm)

        # Navigation buttons
        st.markdown("---")
        col1, col2, col3 = st.columns([1, 2, 1])

        with col1:
            if st.button("🔙 Back to Cross-Validation", key="back_to_cv"):
                self.session.set("model_selection_stage", 3)
                st.rerun()

        with col3:
            if self.session.has("model_training_results"):
                if st.button("➡️ View Results", type="primary", key="proceed_to_results"):
                    self.session.set("model_selection_stage", 5)
                    st.rerun()
            else:
                st.button("➡️ Train Model First", key="train_model_first", disabled=True)

    def _render_optimization_route_selection(self) -> None:
        """Render optimization route selection interface."""
        st.markdown("#### 🛤️ Choose Your Optimization Approach")
        st.markdown("Select how you want to configure and optimize your model parameters:")

        col1, col2 = st.columns(2)

        # Manual Parameter Configuration Route
        with col1:
            manual_card = self._create_route_card(
                title="Manual Parameter Configuration",
                subtitle="Expert Control & Fine-tuning",
                icon="🧠",
                features=[
                    "Domain experts with parameter knowledge",
                    "Specific parameter requirements",
                    "Quick single configuration testing",
                    "Understanding parameter effects"
                ],
                border_color="#2E8B57",
                bg_gradient="linear-gradient(135deg, #f0fff0 0%, #e6ffe6 100%)",
                text_color="#2E8B57"
            )
            st.markdown(manual_card, unsafe_allow_html=True)

            if st.button("🧠 Choose Manual Configuration", key="select_manual_optimization", use_container_width=True):
                self.session.set("selected_optimization_route", "manual")
                st.rerun()

        # Automated Grid Search Route
        with col2:
            auto_card = self._create_route_card(
                title="Automated Grid Search",
                subtitle="Comprehensive Optimization",
                icon="🤖",
                features=[
                    "Automated parameter optimization",
                    "Comprehensive parameter space exploration",
                    "Performance-driven selection",
                    "Finding optimal configurations"
                ],
                border_color="#4169E1",
                bg_gradient="linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%)",
                text_color="#4169E1"
            )
            st.markdown(auto_card, unsafe_allow_html=True)

            if st.button("🤖 Choose Automated Grid Search", key="select_automatic_optimization", use_container_width=True):
                self.session.set("selected_optimization_route", "automatic")
                st.rerun()

        st.markdown("---")

    def _render_manual_optimization_interface(self, x_train: pd.DataFrame, y_train: pd.DataFrame, algorithm: str) -> None:
        """Render manual optimization interface."""
        # Manual route header with styling
        st.markdown("""
<div style="
    padding: 1rem;
    border-left: 5px solid #2E8B57;
    background: linear-gradient(90deg, #f0fff0 0%, #ffffff 100%);
    border-radius: 0 10px 10px 0;
    margin-bottom: 1.5rem;
">
    <h4 style="color: #2E8B57; margin: 0; font-size: 1.3rem;">
        🧠 Manual Parameter Configuration
    </h4>
    <p style="color: #228B22; margin: 0.5rem 0 0 0; font-size: 1rem;">
        🔬 Configure algorithm parameters manually with expert control
    </p>
</div>""", unsafe_allow_html=True)

        # Algorithm-specific parameter configuration
        model_params = self._render_algorithm_specific_parameters(algorithm, x_train)

        # Cross-validation settings
        st.markdown("##### 🔄 Cross-Validation Settings")
        col1, col2 = st.columns(2)

        with col1:
            cv_folds = st.slider(
                "Cross-Validation Folds",
                min_value=3,
                max_value=10,
                value=5,
                help="Number of CV folds for evaluation"
            )

        with col2:
            cv_method = st.selectbox(
                "CV Method",
                ["K-Fold", "Stratified K-Fold"],
                help="Cross-validation method"
            )

        # Train model button
        st.markdown("##### 🚀 Train Model")

        if st.button("🚀 Train Model with Manual Parameters", type="primary", key="train_manual_model"):
            with st.spinner(f"Training {algorithm} with manual parameters..."):
                try:
                    # Convert to numpy arrays
                    x_array = x_train.select_dtypes(include=[np.number]).values
                    y_array = y_train.select_dtypes(include=[np.number]).values

                    if y_array.ndim == 2 and y_array.shape[1] == 1:
                        y_array = y_array.ravel()

                    # Create CV configuration
                    cv_config = {
                        "method": cv_method,
                        "params": {"n_splits": cv_folds},
                        "mode": "manual"
                    }

                    # Train model with manual parameters using the proper CV function
                    results = self._train_model_with_cv(x_array, y_array, algorithm, model_params, cv_config)

                    # Store results
                    self.session.set("model_training_results", {
                        "optimization_route": "manual",
                        "algorithm": algorithm,
                        "model_params": model_params,
                        "cv_folds": cv_folds,
                        "cv_method": cv_method,
                        "results": results
                    })

                    st.success(f"✅ Model training complete! RMSECV: {results['rmsecv']:.4f}, R²: {results['r2']:.4f}")
                    st.rerun()

                except Exception as e:
                    st.error(f"Error training model: {str(e)}")

    def _render_automatic_optimization_interface(self, x_train: pd.DataFrame, y_train: pd.DataFrame, algorithm: str) -> None:
        """Render automatic optimization interface."""
        # Automatic route header with styling
        st.markdown("""
<div style="
    padding: 1rem;
    border-left: 5px solid #4169E1;
    background: linear-gradient(90deg, #f0f8ff 0%, #ffffff 100%);
    border-radius: 0 10px 10px 0;
    margin-bottom: 1.5rem;
">
    <h4 style="color: #4169E1; margin: 0; font-size: 1.3rem;">
        🤖 Automated Grid Search Optimization
    </h4>
    <p style="color: #1E90FF; margin: 0.5rem 0 0 0; font-size: 1rem;">
        🔬 Automated parameter optimization across multiple configurations
    </p>
</div>""", unsafe_allow_html=True)

        # Grid search configuration
        st.markdown("##### 🔍 Grid Search Configuration")

        col1, col2, col3 = st.columns(3)

        with col1:
            cv_folds = st.slider(
                "Cross-Validation Folds",
                min_value=3,
                max_value=10,
                value=5,
                help="Number of CV folds for evaluation"
            )

        with col2:
            optimization_metric = st.selectbox(
                "Optimization Metric",
                ["RMSECV", "R²", "Combined Score"],
                help="Metric to optimize during grid search"
            )

        with col3:
            n_jobs = st.slider(
                "Parallel Jobs",
                min_value=1,
                max_value=4,
                value=2,
                help="Number of parallel jobs for grid search"
            )

        # Show grid search info
        self._show_algorithm_grid_search_info(algorithm)

        # Run grid search button
        st.markdown("##### 🚀 Run Grid Search")

        if st.button("🚀 Run Grid Search Optimization", type="primary", key="run_grid_search_optimization"):
            with st.spinner(f"Running grid search optimization for {algorithm}... This may take several minutes."):
                try:
                    # Convert to numpy arrays
                    x_array = x_train.select_dtypes(include=[np.number]).values
                    y_array = y_train.select_dtypes(include=[np.number]).values

                    if y_array.ndim == 2 and y_array.shape[1] == 1:
                        y_array = y_array.ravel()

                    # Run grid search
                    results = self._run_algorithm_grid_search(
                        x_array, y_array, algorithm, cv_folds, optimization_metric, n_jobs
                    )

                    # Store results
                    self.session.set("model_training_results", {
                        "optimization_route": "automatic",
                        "algorithm": algorithm,
                        "cv_folds": cv_folds,
                        "optimization_metric": optimization_metric,
                        "n_jobs": n_jobs,
                        "results": results
                    })

                    st.success(f"✅ Grid search complete! Best RMSECV: {results['best_rmsecv']:.4f}")
                    st.rerun()

                except Exception as e:
                    st.error(f"Error during grid search: {str(e)}")

    def _render_algorithm_specific_parameters(self, algorithm: str, x_train: pd.DataFrame) -> Dict[str, Any]:
        """Render algorithm-specific parameter controls."""
        params = {}

        if algorithm in ["NIPALS (PLS1, PLS2)", "SIMPLS"]:
            # PLS parameters
            col1, col2 = st.columns(2)
            with col1:
                params['n_components'] = st.slider(
                    "Number of Components",
                    min_value=1,
                    max_value=min(15, x_train.shape[1], x_train.shape[0]-2),
                    value=min(self.session.get("recommended_components", 5), x_train.shape[1]//2),
                    help="Number of PLS components"
                )
            with col2:
                if algorithm == "NIPALS (PLS1, PLS2)":
                    params['mode'] = st.selectbox("PLS Mode", ["PLS1", "PLS2"], help="PLS1 for single response, PLS2 for multiple responses")
                    params['max_iter'] = st.slider("Max Iterations", 100, 1000, 500, help="Maximum iterations for NIPALS algorithm")
                    params['tol'] = st.select_slider("Tolerance", options=[1e-6, 1e-5, 1e-4, 1e-3], value=1e-6, help="Convergence tolerance")

        elif algorithm == "Multilayer Perceptron (MLP)":
            # MLP parameters
            col1, col2 = st.columns(2)
            with col1:
                params['hidden_layer_sizes'] = st.selectbox(
                    "Hidden Layer Architecture",
                    [(50,), (100,), (50, 50), (100, 50), (100, 100), (100, 50, 25)],
                    index=1,
                    help="Architecture of hidden layers"
                )
                params['activation'] = st.selectbox("Activation Function", ['relu', 'tanh', 'logistic'], help="Activation function for hidden layers")
                params['solver'] = st.selectbox("Solver", ['adam', 'lbfgs', 'sgd'], help="Solver for weight optimization")
            with col2:
                params['alpha'] = st.select_slider("Regularization (α)", options=[0.0001, 0.001, 0.01, 0.1], value=0.0001, help="L2 penalty parameter")
                params['learning_rate'] = st.selectbox("Learning Rate", ['constant', 'invscaling', 'adaptive'], help="Learning rate schedule")
                params['max_iter'] = st.slider("Max Iterations", 100, 1000, 200, help="Maximum iterations")

        elif algorithm == "Enhanced ANN (MATLAB-style)":
            # Enhanced ANN parameters with MATLAB-style options
            st.info("📋 **Note**: Data preprocessing is handled by Steps 3 & 4. This interface focuses on neural network architecture and training.")

            col1, col2 = st.columns(2)
            with col1:
                params['hidden_layer_sizes'] = st.selectbox(
                    "Hidden Layer Architecture",
                    [(5,), (10,), (20,), (10, 10), (20, 10), (30, 20, 10)],
                    index=1,
                    help="Architecture of hidden layers (MATLAB-style)"
                )
                params['activation'] = st.selectbox("Activation Function", ["tanh", "relu", "logistic"], index=0, help="tanh is equivalent to MATLAB tansig")
                params['solver'] = st.selectbox("Training Algorithm", ["lbfgs", "adam", "sgd"], index=0, help="lbfgs is equivalent to MATLAB trainlm")
                params['max_iter'] = st.slider("Max Iterations", 100, 1000, 500, help="Maximum training iterations")
            with col2:
                params['data_division'] = st.selectbox("Data Division", ["sequential", "random", "interleaved"], index=0, help="sequential is like MATLAB divideblock")
                params['train_ratio'] = st.slider("Training Ratio", 0.4, 0.8, 0.48, help="Fraction for training")
                params['val_ratio'] = st.slider("Validation Ratio", 0.1, 0.3, 0.16, help="Fraction for validation")
                params['test_ratio'] = 1.0 - params['train_ratio'] - params['val_ratio']
                st.write(f"Test Ratio: {params['test_ratio']:.2f}")
                params['early_stopping'] = st.checkbox("Early Stopping", value=True, help="Stop training when validation score stops improving")

        elif algorithm == "Backpropagation Neural Network (BPNN)":
            # BPNN parameters (similar to MLP but with specific settings)
            col1, col2 = st.columns(2)
            with col1:
                params['hidden_layer_sizes'] = st.selectbox(
                    "Hidden Layer Architecture",
                    [(10,), (20,), (10, 10), (20, 10), (30, 20, 10)],
                    index=2,
                    help="Architecture of hidden layers for BPNN"
                )
                params['learning_rate_init'] = st.select_slider("Initial Learning Rate", options=[0.001, 0.01, 0.1, 0.2], value=0.01)
            with col2:
                params['momentum'] = st.slider("Momentum", 0.0, 1.0, 0.9, help="Momentum for gradient descent")
                params['max_iter'] = st.slider("Max Iterations", 100, 500, 200, help="Maximum iterations")

        elif algorithm == "ε-Support Vector Regression (ε-SVR)":
            # SVR parameters
            col1, col2 = st.columns(2)
            with col1:
                params['C'] = st.select_slider("Regularization (C)", options=[0.1, 1.0, 10.0, 100.0], value=1.0, help="Regularization parameter")
                params['epsilon'] = st.select_slider("Epsilon", options=[0.01, 0.1, 0.2, 0.5], value=0.1, help="Epsilon in epsilon-SVR model")
                params['kernel'] = st.selectbox("Kernel", ['rbf', 'linear', 'poly', 'sigmoid'], help="Kernel type")
            with col2:
                if params['kernel'] in ['rbf', 'poly', 'sigmoid']:
                    params['gamma'] = st.selectbox("Gamma", ['scale', 'auto'], help="Kernel coefficient")
                if params['kernel'] == 'poly':
                    params['degree'] = st.slider("Polynomial Degree", 2, 5, 3, help="Degree for polynomial kernel")

        elif algorithm == "Nu-Support Vector Regression (Nu-SVR)":
            # Nu-SVR parameters
            col1, col2 = st.columns(2)
            with col1:
                params['C'] = st.select_slider("Regularization (C)", options=[0.1, 1.0, 10.0, 100.0], value=1.0, help="Regularization parameter")
                params['nu'] = st.slider("Nu", 0.01, 1.0, 0.5, help="Upper bound on fraction of training errors")
                params['kernel'] = st.selectbox("Kernel", ['rbf', 'linear', 'poly', 'sigmoid'], help="Kernel type")
            with col2:
                if params['kernel'] in ['rbf', 'poly', 'sigmoid']:
                    params['gamma'] = st.selectbox("Gamma", ['scale', 'auto'], help="Kernel coefficient")

        elif algorithm == "XGBoost" and HAS_XGBOOST:
            # XGBoost parameters
            col1, col2 = st.columns(2)
            with col1:
                params['n_estimators'] = st.slider("Number of Estimators", 50, 500, 100, help="Number of boosting rounds")
                params['max_depth'] = st.slider("Max Depth", 3, 10, 6, help="Maximum tree depth")
                params['learning_rate'] = st.select_slider("Learning Rate", options=[0.01, 0.1, 0.2, 0.3], value=0.1, help="Boosting learning rate")
            with col2:
                params['subsample'] = st.slider("Subsample", 0.5, 1.0, 0.8, help="Subsample ratio of training instances")
                params['colsample_bytree'] = st.slider("Feature Subsample", 0.5, 1.0, 0.8, help="Subsample ratio of features")
                params['reg_alpha'] = st.select_slider("L1 Regularization", options=[0, 0.01, 0.1, 1.0], value=0, help="L1 regularization term")
                params['reg_lambda'] = st.select_slider("L2 Regularization", options=[0, 0.01, 0.1, 1.0], value=1.0, help="L2 regularization term")

        return params











    def _run_svr_grid_search(self, X: np.ndarray, y: np.ndarray, kf, optimization_metric: str, algorithm: str) -> Dict[str, Any]:
        """Run grid search for SVR algorithms."""
        if "ε-SVR" in algorithm:
            # ε-SVR parameters
            C_values = [0.1, 1.0, 10.0, 100.0]
            epsilon_values = [0.01, 0.1, 0.2]
            kernels = ['rbf', 'linear']

            best_rmsecv = float('inf')
            best_r2 = -float('inf')
            best_params = {}
            best_model = None
            grid_results = []

            for C in C_values:
                for epsilon in epsilon_values:
                    for kernel in kernels:
                        try:
                            model = SVR(C=C, epsilon=epsilon, kernel=kernel)

                            scores = cross_val_score(model, X, y, cv=kf, scoring='neg_mean_squared_error')
                            rmsecv = np.sqrt(-scores.mean())

                            r2_scores = cross_val_score(model, X, y, cv=kf, scoring='r2')
                            r2 = r2_scores.mean()

                            params = {'C': C, 'epsilon': epsilon, 'kernel': kernel}

                            grid_results.append({
                                'rmsecv': rmsecv,
                                'r2': r2,
                                'params': params
                            })

                            if (optimization_metric == 'RMSECV' and rmsecv < best_rmsecv) or \
                               (optimization_metric == 'R²' and r2 > best_r2):
                                best_rmsecv = rmsecv
                                best_r2 = r2
                                best_params = params
                                best_model = model

                        except Exception:
                            continue

        elif "Nu-SVR" in algorithm:
            # Nu-SVR parameters
            C_values = [0.1, 1.0, 10.0]
            nu_values = [0.1, 0.3, 0.5, 0.7]
            kernels = ['rbf', 'linear']

            best_rmsecv = float('inf')
            best_r2 = -float('inf')
            best_params = {}
            best_model = None
            grid_results = []

            for C in C_values:
                for nu in nu_values:
                    for kernel in kernels:
                        try:
                            model = NuSVR(C=C, nu=nu, kernel=kernel)

                            # Use cross-validation for both RMSECV and R²
                            scores = cross_val_score(model, X, y, cv=kf, scoring='neg_mean_squared_error')
                            rmsecv = np.sqrt(-scores.mean())

                            r2_scores = cross_val_score(model, X, y, cv=kf, scoring='r2')
                            r2 = r2_scores.mean()

                            params = {'C': C, 'nu': nu, 'kernel': kernel}

                            grid_results.append({
                                'rmsecv': rmsecv,
                                'r2': r2,
                                'params': params
                            })

                            if (optimization_metric == 'RMSECV' and rmsecv < best_rmsecv) or \
                               (optimization_metric == 'R²' and r2 > best_r2):
                                best_rmsecv = rmsecv
                                best_r2 = r2
                                best_params = params
                                best_model = model

                        except Exception:
                            continue

        if best_model is not None:
            best_model.fit(X, y)

        return {
            'best_model': best_model,
            'best_rmsecv': best_rmsecv,
            'best_r2': best_r2,
            'best_params': best_params,
            'grid_results': grid_results,
            'algorithm': algorithm,
            'optimization_metric': optimization_metric
        }

    def _run_xgboost_grid_search(self, X: np.ndarray, y: np.ndarray, kf, optimization_metric: str) -> Dict[str, Any]:
        """Run grid search for XGBoost."""
        if not HAS_XGBOOST:
            return self._run_basic_algorithm_evaluation(X, y, kf, "XGBoost", optimization_metric)

        import xgboost as xgb

        # Parameter grid for XGBoost
        n_estimators = [50, 100, 200]
        max_depths = [3, 6, 9]
        learning_rates = [0.01, 0.1, 0.2]

        best_rmsecv = float('inf')
        best_r2 = -float('inf')
        best_params = {}
        best_model = None
        grid_results = []

        for n_est in n_estimators:
            for max_depth in max_depths:
                for lr in learning_rates:
                    try:
                        model = xgb.XGBRegressor(
                            n_estimators=n_est,
                            max_depth=max_depth,
                            learning_rate=lr,
                            random_state=42
                        )

                        # Use cross-validation for both RMSECV and R²
                        scores = cross_val_score(model, X, y, cv=kf, scoring='neg_mean_squared_error')
                        rmsecv = np.sqrt(-scores.mean())

                        r2_scores = cross_val_score(model, X, y, cv=kf, scoring='r2')
                        r2 = r2_scores.mean()

                        params = {
                            'n_estimators': n_est,
                            'max_depth': max_depth,
                            'learning_rate': lr
                        }

                        grid_results.append({
                            'rmsecv': rmsecv,
                            'r2': r2,
                            'params': params
                        })

                        if (optimization_metric == 'RMSECV' and rmsecv < best_rmsecv) or \
                           (optimization_metric == 'R²' and r2 > best_r2):
                            best_rmsecv = rmsecv
                            best_r2 = r2
                            best_params = params
                            best_model = model

                    except Exception:
                        continue

        if best_model is not None:
            best_model.fit(X, y)

        return {
            'best_model': best_model,
            'best_rmsecv': best_rmsecv,
            'best_r2': best_r2,
            'best_params': best_params,
            'grid_results': grid_results,
            'algorithm': 'XGBoost',
            'optimization_metric': optimization_metric
        }

    def _run_basic_algorithm_evaluation(self, X: np.ndarray, y: np.ndarray, kf, algorithm: str, optimization_metric: str) -> Dict[str, Any]:
        """Run basic evaluation for algorithms without specific grid search."""
        try:
            # Create a basic model based on algorithm
            if "NIPALS" in algorithm:
                model = NIPALSRegression(n_components=min(10, X.shape[1]//2))
            elif "SIMPLS" in algorithm:
                model = PLSRegression(n_components=min(10, X.shape[1]//2))
            else:
                # Fallback to PLS
                model = PLSRegression(n_components=min(5, X.shape[1]//2))

            # Use improved cross-validation
            rmsecv, cv_scores = self._perform_manual_cv(model, X, y, kf)

            # Train on full dataset
            model.fit(X, y)

            return {
                'best_model': model,
                'best_rmsecv': rmsecv,
                'best_params': {'n_components': model.n_components},
                'grid_results': [{'rmsecv': rmsecv, 'params': {'n_components': model.n_components}}],
                'algorithm': algorithm,
                'optimization_metric': optimization_metric
            }

        except Exception as e:
            # Ultimate fallback
            return {
                'best_model': None,
                'best_rmsecv': 1.0,
                'best_r2': 0.0,
                'best_params': {},
                'grid_results': [],
                'algorithm': algorithm,
                'optimization_metric': optimization_metric
            }

    def _perform_multi_component_cv(self, X: np.ndarray, y: np.ndarray, n_latent_vars: int, kf, algorithm: str) -> Dict[str, Any]:
        """Perform cross-validation with component-specific analysis."""
        n_samples, n_components_y = y.shape

        # Initialize storage for predictions and errors
        cv_predictions = np.zeros((n_samples, n_components_y))
        component_errors = []
        component_r2_scores = []

        # Perform cross-validation
        for train_idx, test_idx in kf.split(X):
            X_train_fold, X_test_fold = X[train_idx], X[test_idx]
            y_train_fold, y_test_fold = y[train_idx], y[test_idx]

            # Fit PLS model
            model = PLSRegression(n_components=n_latent_vars)
            model.fit(X_train_fold, y_train_fold)

            # Predict test set
            y_pred_fold = model.predict(X_test_fold)

            # Store predictions
            cv_predictions[test_idx] = y_pred_fold

            # Calculate component-specific errors for this fold
            fold_errors = []
            fold_r2 = []
            for comp_idx in range(n_components_y):
                # RMSE for this component in this fold
                rmse_comp = np.sqrt(np.mean((y_test_fold[:, comp_idx] - y_pred_fold[:, comp_idx]) ** 2))
                fold_errors.append(rmse_comp)

                # R² for this component in this fold
                ss_res = np.sum((y_test_fold[:, comp_idx] - y_pred_fold[:, comp_idx]) ** 2)
                ss_tot = np.sum((y_test_fold[:, comp_idx] - np.mean(y_test_fold[:, comp_idx])) ** 2)
                r2_comp = 1 - (ss_res / ss_tot) if ss_tot > 0 else 0
                fold_r2.append(r2_comp)

            component_errors.append(fold_errors)
            component_r2_scores.append(fold_r2)

        # Calculate final component-specific metrics
        component_errors = np.array(component_errors)  # Shape: (n_folds, n_components_y)
        component_r2_scores = np.array(component_r2_scores)  # Shape: (n_folds, n_components_y)

        # Mean RMSECV for each component
        component_rmsecv = np.mean(component_errors, axis=0)

        # Mean R² for each component
        component_r2 = np.mean(component_r2_scores, axis=0)

        # Overall metrics (averaged across components)
        overall_rmsecv = np.mean(component_rmsecv)
        overall_r2 = np.mean(component_r2)

        # Generate component names
        y_train_df = self.session.get("y_train")
        if y_train_df is not None and hasattr(y_train_df, 'columns'):
            component_names = list(y_train_df.columns)
        else:
            component_names = [f"Component {i+1}" for i in range(n_components_y)]

        return {
            'overall_rmsecv': overall_rmsecv,
            'overall_r2': overall_r2,
            'component_rmsecv': component_rmsecv,
            'component_r2': component_r2,
            'component_names': component_names,
            'cv_predictions': cv_predictions
        }

    def _train_single_model(self, X: np.ndarray, y: np.ndarray, algorithm: str, params: Dict[str, Any], cv_folds: int) -> Dict[str, Any]:
        """Train a single model with given parameters."""
        kf = KFold(n_splits=cv_folds, shuffle=True, random_state=42)

        try:
            # PLS implementations
            if algorithm in ["NIPALS (PLS1, PLS2)"]:
                # Use NIPALS implementation
                mode = params.get('mode', 'PLS1')
                model = NIPALSRegression(
                    n_components=params.get('n_components', 5),
                    mode=mode,
                    max_iter=params.get('max_iter', 500),
                    tol=params.get('tol', 1e-6)
                )
                # Use improved cross-validation
                rmsecv, cv_scores = self._perform_manual_cv(model, X, y, kf)

                # Train final model
                model.fit(X, y)

                return {
                    'model': model,
                    'rmsecv': rmsecv,
                    'cv_scores': cv_scores
                }

            elif algorithm == "SIMPLS":
                # Use scikit-learn PLSRegression (SIMPLS)
                model = PLSRegression(
                    n_components=params.get('n_components', 5),
                    scale=params.get('scale', True)
                )
                # Use improved cross-validation
                rmsecv, cv_scores = self._perform_manual_cv(model, X, y, kf)

                # Train final model
                model.fit(X, y)

                return {
                    'model': model,
                    'rmsecv': rmsecv,
                    'cv_scores': cv_scores
                }

            elif algorithm == "Enhanced ANN (MATLAB-style)":
                # Use Enhanced ANN implementation
                from utils.enhanced_ann import EnhancedANN

                # Map MATLAB-style activation names to scikit-learn names
                activation_mapping = {
                    'tansig': 'tanh',
                    'logsig': 'logistic',
                    'purelin': 'identity'
                }
                activation = params.get('activation', 'tansig')
                mapped_activation = activation_mapping.get(activation, 'tanh')

                # Map MATLAB-style solver names to scikit-learn names
                solver_mapping = {
                    'trainlm': 'lbfgs',
                    'trainbr': 'lbfgs',
                    'traingd': 'sgd',
                    'trainscg': 'adam'
                }
                solver = params.get('training_algorithm', 'trainlm')
                mapped_solver = solver_mapping.get(solver, 'lbfgs')

                model = EnhancedANN(
                    hidden_layer_sizes=params.get('hidden_layer_sizes', (50,)),
                    activation=mapped_activation,
                    solver=mapped_solver,
                    data_division=params.get('data_division', 'random'),
                    train_ratio=params.get('train_ratio', 0.7),
                    val_ratio=params.get('val_ratio', 0.15),
                    max_iter=params.get('max_epochs', 300),
                    random_state=42
                )
                # Use improved cross-validation
                rmsecv, cv_scores = self._perform_manual_cv(model, X, y, kf)

                # Train final model
                model.fit(X, y)

                return {
                    'model': model,
                    'rmsecv': rmsecv,
                    'cv_scores': cv_scores
                }

            elif algorithm == "Multilayer Perceptron (MLP)":
                # Use standard MLPRegressor
                model = MLPRegressor(
                    hidden_layer_sizes=params.get('hidden_layer_sizes', (100,)),
                    activation=params.get('activation', 'relu'),
                    solver=params.get('solver', 'adam'),
                    alpha=params.get('alpha', 0.0001),
                    learning_rate=params.get('learning_rate', 'constant'),
                    learning_rate_init=params.get('learning_rate_init', 0.001),
                    max_iter=params.get('max_iter', 200),
                    early_stopping=params.get('early_stopping', True),
                    random_state=42
                )
                # Use improved cross-validation
                rmsecv, cv_scores = self._perform_manual_cv(model, X, y, kf)

                # Train final model
                model.fit(X, y)

                return {
                    'model': model,
                    'rmsecv': rmsecv,
                    'cv_scores': cv_scores
                }

            elif algorithm == "ε-Support Vector Regression (ε-SVR)":
                # Use SVR
                model = SVR(
                    C=params.get('C', 1.0),
                    epsilon=params.get('epsilon', 0.1),
                    kernel=params.get('kernel', 'rbf'),
                    gamma=params.get('gamma', 'scale'),
                    degree=params.get('degree', 3)
                )
                # Use improved cross-validation
                rmsecv, cv_scores = self._perform_manual_cv(model, X, y, kf)

                # Train final model
                model.fit(X, y)

                return {
                    'model': model,
                    'rmsecv': rmsecv,
                    'cv_scores': cv_scores
                }

            elif algorithm == "Nu-Support Vector Regression (Nu-SVR)":
                # Use NuSVR
                model = NuSVR(
                    C=params.get('C', 1.0),
                    nu=params.get('nu', 0.5),
                    kernel=params.get('kernel', 'rbf'),
                    gamma=params.get('gamma', 'scale'),
                    degree=params.get('degree', 3)
                )
                # Use improved cross-validation
                rmsecv, cv_scores = self._perform_manual_cv(model, X, y, kf)

                # Train final model
                model.fit(X, y)

                return {
                    'model': model,
                    'rmsecv': rmsecv,
                    'cv_scores': cv_scores
                }

            elif algorithm == "XGBoost" and HAS_XGBOOST:
                # Use XGBoost
                import xgboost as xgb
                model = xgb.XGBRegressor(
                    n_estimators=params.get('n_estimators', 100),
                    max_depth=params.get('max_depth', 6),
                    learning_rate=params.get('learning_rate', 0.1),
                    subsample=params.get('subsample', 0.8),
                    colsample_bytree=params.get('colsample_bytree', 0.8),
                    reg_alpha=params.get('reg_alpha', 0),
                    reg_lambda=params.get('reg_lambda', 1.0),
                    random_state=42
                )
                # Use improved cross-validation
                rmsecv, cv_scores = self._perform_manual_cv(model, X, y, kf)

                # Train final model
                model.fit(X, y)

                return {
                    'model': model,
                    'rmsecv': rmsecv,
                    'cv_scores': cv_scores
                }

            else:
                # Fallback to PLS if algorithm not recognized
                st.warning(f"⚠️ Algorithm '{algorithm}' not fully implemented. Using SIMPLS as fallback.")
                model = PLSRegression(n_components=5)
                rmsecv, cv_scores = self._perform_manual_cv(model, X, y, kf)
                model.fit(X, y)

                return {
                    'model': model,
                    'rmsecv': rmsecv,
                    'cv_scores': cv_scores
                }

        except Exception as e:
            st.error(f"❌ Error training {algorithm}: {str(e)}")
            # Return fallback result
            return {
                'model': None,
                'rmsecv': 999.0,
                'cv_scores': []
            }

    def _render_grid_search_results_display(self, results: Dict[str, Any]) -> None:
        """Render comprehensive grid search results display with visualizations."""
        st.markdown("#### 🔍 Grid Search Optimization Results")

        grid_results = results["results"].get("grid_results", [])
        algorithm = results["algorithm"]
        best_params = results["results"].get("best_params", {})
        best_rmsecv = results["results"].get("best_rmsecv", 0)

        if grid_results:
            # Convert to DataFrame for analysis
            import pandas as pd
            df = pd.DataFrame(grid_results)

            # Summary statistics
            st.markdown("##### 📊 Optimization Summary")
            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("Total Configurations", len(df))
            with col2:
                st.metric("Best RMSECV", f"{best_rmsecv:.4f}")
            with col3:
                improvement = ((df['rmsecv'].max() - best_rmsecv) / df['rmsecv'].max() * 100)
                st.metric("RMSECV Improvement", f"{improvement:.1f}%")

            # Best parameters found
            if best_params:
                st.markdown("##### 🏆 Optimal Parameters")
                st.markdown(f"""
<div style="
    padding: 1rem 1.5rem;
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    border-left: 5px solid #4CAF50;
    border-radius: 8px;
    margin: 1rem 0;
    font-family: 'Nunito Sans', sans-serif;
    box-shadow: 0 2px 4px rgba(76, 175, 80, 0.1);
">""", unsafe_allow_html=True)

                for param, value in best_params.items():
                    st.markdown(f"**{param.replace('_', ' ').title()}**: {value}")

                st.markdown("</div>", unsafe_allow_html=True)

            # Visualization of parameter optimization
            self._render_parameter_optimization_plots(df, algorithm)

            # Detailed results table
            st.markdown("##### 📋 Detailed Results Table")

            # Sort by RMSECV and show top results
            df_sorted = df.sort_values('rmsecv').reset_index(drop=True)
            df_display = df_sorted.head(10).copy()
            df_display['rmsecv'] = df_display['rmsecv'].round(4)
            df_display.index = df_display.index + 1  # Start from 1

            st.markdown("**Top 10 Best Configurations:**")
            st.dataframe(df_display, use_container_width=True)

            # Performance distribution
            self._render_performance_distribution(df)

        else:
            st.warning("⚠️ No detailed grid search results available for visualization.")

    def _render_parameter_optimization_plots(self, df: pd.DataFrame, algorithm: str) -> None:
        """Render comprehensive parameter optimization visualization plots."""
        st.markdown("##### 📈 Parameter Optimization Visualization")

        import plotly.graph_objects as go
        import plotly.express as px
        from plotly.subplots import make_subplots

        # Create different plots based on algorithm type
        if algorithm in ["NIPALS (PLS1, PLS2)", "SIMPLS"]:
            # Check if we have component-specific results
            results = self.session.get("model_training_results", {})
            component_results = results.get("results", {}).get("component_results", [])
            n_components_y = results.get("results", {}).get("n_components_y", 1)

            if component_results and n_components_y > 1:
                # Multi-component analysis with component-specific plots
                self._render_multi_component_optimization_plots(df, component_results, algorithm)
            elif 'n_components' in df.columns:
                # Create comprehensive PLS analysis plots
                fig = make_subplots(
                    rows=2, cols=2,
                    subplot_titles=(
                        'RMSECV vs Components',
                        'R² vs Components',
                        'RMSECV Improvement Rate',
                        'Performance Comparison'
                    ),
                    specs=[[{"secondary_y": False}, {"secondary_y": False}],
                           [{"secondary_y": False}, {"secondary_y": False}]]
                )

                # Sort by components for proper line plotting
                df_sorted = df.sort_values('n_components').reset_index(drop=True)

                # 1. RMSECV vs Components
                fig.add_trace(
                    go.Scatter(
                        x=df_sorted['n_components'],
                        y=df_sorted['rmsecv'],
                        mode='lines+markers',
                        name='RMSECV',
                        line=dict(color='red', width=3),
                        marker=dict(size=10),
                        hovertemplate='Components: %{x}<br>RMSECV: %{y:.4f}<extra></extra>'
                    ),
                    row=1, col=1
                )

                # 2. R² vs Components
                fig.add_trace(
                    go.Scatter(
                        x=df_sorted['n_components'],
                        y=df_sorted['r2'],
                        mode='lines+markers',
                        name='R²',
                        line=dict(color='blue', width=3),
                        marker=dict(size=10),
                        hovertemplate='Components: %{x}<br>R²: %{y:.4f}<extra></extra>'
                    ),
                    row=1, col=2
                )

                # 3. RMSECV Improvement Rate
                if len(df_sorted) > 1:
                    improvement_rates = []
                    components_for_improvement = []
                    for i in range(1, len(df_sorted)):
                        prev_rmsecv = df_sorted.iloc[i-1]['rmsecv']
                        curr_rmsecv = df_sorted.iloc[i]['rmsecv']
                        improvement = (prev_rmsecv - curr_rmsecv) / prev_rmsecv * 100
                        improvement_rates.append(improvement)
                        components_for_improvement.append(df_sorted.iloc[i]['n_components'])

                    fig.add_trace(
                        go.Bar(
                            x=components_for_improvement,
                            y=improvement_rates,
                            name='RMSECV Improvement %',
                            marker_color='green',
                            opacity=0.7,
                            hovertemplate='Components: %{x}<br>Improvement: %{y:.2f}%<extra></extra>'
                        ),
                        row=2, col=1
                    )

                # 4. Performance Comparison (Normalized)
                # Normalize both metrics to 0-1 scale for comparison
                rmsecv_norm = (df_sorted['rmsecv'].max() - df_sorted['rmsecv']) / (df_sorted['rmsecv'].max() - df_sorted['rmsecv'].min())
                r2_norm = (df_sorted['r2'] - df_sorted['r2'].min()) / (df_sorted['r2'].max() - df_sorted['r2'].min())

                fig.add_trace(
                    go.Scatter(
                        x=df_sorted['n_components'],
                        y=rmsecv_norm,
                        mode='lines+markers',
                        name='RMSECV (normalized)',
                        line=dict(color='red', width=2, dash='dash'),
                        marker=dict(size=8)
                    ),
                    row=2, col=2
                )

                fig.add_trace(
                    go.Scatter(
                        x=df_sorted['n_components'],
                        y=r2_norm,
                        mode='lines+markers',
                        name='R² (normalized)',
                        line=dict(color='blue', width=2, dash='dash'),
                        marker=dict(size=8)
                    ),
                    row=2, col=2
                )

                # Highlight best point on all relevant plots
                best_idx = df['rmsecv'].idxmin()
                best_comp = df.loc[best_idx, 'n_components']
                best_rmsecv = df.loc[best_idx, 'rmsecv']
                best_r2 = df.loc[best_idx, 'r2']

                # Add best point markers
                fig.add_trace(
                    go.Scatter(
                        x=[best_comp],
                        y=[best_rmsecv],
                        mode='markers',
                        name='Optimal Point',
                        marker=dict(color='gold', size=20, symbol='star', line=dict(color='black', width=2)),
                        showlegend=True
                    ),
                    row=1, col=1
                )

                fig.add_trace(
                    go.Scatter(
                        x=[best_comp],
                        y=[best_r2],
                        mode='markers',
                        name='Optimal Point',
                        marker=dict(color='gold', size=20, symbol='star', line=dict(color='black', width=2)),
                        showlegend=False
                    ),
                    row=1, col=2
                )

                # Update layout
                fig.update_layout(
                    title=f"{algorithm} - Comprehensive Parameter Optimization Analysis",
                    height=800,
                    showlegend=True,
                    template="plotly_white"
                )

                # Update axes labels
                fig.update_xaxes(title_text="Number of Components", row=1, col=1)
                fig.update_xaxes(title_text="Number of Components", row=1, col=2)
                fig.update_xaxes(title_text="Number of Components", row=2, col=1)
                fig.update_xaxes(title_text="Number of Components", row=2, col=2)

                fig.update_yaxes(title_text="RMSECV", row=1, col=1)
                fig.update_yaxes(title_text="R²", row=1, col=2)
                fig.update_yaxes(title_text="Improvement (%)", row=2, col=1)
                fig.update_yaxes(title_text="Normalized Performance", row=2, col=2)

                st.plotly_chart(fig, use_container_width=True)

                # Enhanced optimization insights
                st.markdown("##### 🔍 Detailed Optimization Analysis")

                col1, col2, col3 = st.columns(3)

                with col1:
                    st.markdown("**🎯 Optimal Configuration:**")
                    st.write(f"• **Components**: {best_comp}")
                    st.write(f"• **RMSECV**: {best_rmsecv:.4f}")
                    st.write(f"• **R²**: {best_r2:.4f}")

                    # Performance ranking
                    total_configs = len(df)
                    best_rank = df['rmsecv'].rank().loc[best_idx]
                    st.write(f"• **Rank**: {int(best_rank)} of {total_configs}")

                with col2:
                    st.markdown("**📈 Improvement Analysis:**")
                    if len(df_sorted) > 1:
                        # Find largest improvement
                        if improvement_rates:
                            max_improvement = max(improvement_rates)
                            max_improvement_comp = components_for_improvement[improvement_rates.index(max_improvement)]
                            st.write(f"• **Largest Improvement**: {max_improvement:.2f}%")
                            st.write(f"• **At Components**: {max_improvement_comp}")

                        # Diminishing returns analysis
                        if len(improvement_rates) > 2:
                            recent_improvements = improvement_rates[-3:]
                            avg_recent = np.mean(recent_improvements)
                            if avg_recent < 1.0:
                                st.write("• **Status**: Diminishing returns")
                            else:
                                st.write("• **Status**: Still improving")

                with col3:
                    st.markdown("**⚖️ Performance Trade-offs:**")
                    # Calculate efficiency metrics
                    efficiency = best_r2 / best_comp  # R² per component
                    st.write(f"• **R²/Component**: {efficiency:.4f}")

                    # Compare with simpler models
                    if best_comp > 1:
                        simpler_models = df_sorted[df_sorted['n_components'] < best_comp]
                        if not simpler_models.empty:
                            best_simpler = simpler_models.loc[simpler_models['rmsecv'].idxmin()]
                            complexity_benefit = (best_simpler['rmsecv'] - best_rmsecv) / best_simpler['rmsecv'] * 100
                            st.write(f"• **Complexity Benefit**: {complexity_benefit:.2f}%")
                            st.write(f"• **vs {int(best_simpler['n_components'])} components**")

                # Elbow point detection
                if len(df_sorted) > 3:
                    st.markdown("##### 🔄 Elbow Point Analysis")

                    # Calculate second derivatives to find elbow
                    rmsecv_values = df_sorted['rmsecv'].values
                    if len(rmsecv_values) > 2:
                        # Calculate first and second derivatives
                        first_deriv = np.diff(rmsecv_values)
                        second_deriv = np.diff(first_deriv)

                        if len(second_deriv) > 0:
                            # Find elbow point (maximum second derivative)
                            elbow_idx = np.argmax(np.abs(second_deriv)) + 2  # +2 because of double diff
                            elbow_comp = df_sorted.iloc[elbow_idx]['n_components']
                            elbow_rmsecv = df_sorted.iloc[elbow_idx]['rmsecv']

                            col1, col2 = st.columns(2)
                            with col1:
                                st.info(f"🎯 **Elbow Point Detected**: {elbow_comp} components (RMSECV: {elbow_rmsecv:.4f})")
                            with col2:
                                if elbow_comp == best_comp:
                                    st.success("✅ Optimal point matches elbow point!")
                                else:
                                    st.warning(f"⚠️ Consider {elbow_comp} components for simpler model")

        else:
            # Enhanced visualization for non-PLS algorithms
            self._render_advanced_algorithm_optimization_plots(df, algorithm)

    def _render_multi_component_optimization_plots(self, df: pd.DataFrame, component_results: List[Dict], algorithm: str) -> None:
        """Render multi-component optimization plots showing RMSECV vs latent variables for each component."""
        import plotly.graph_objects as go
        from plotly.subplots import make_subplots

        # Extract component information
        if not component_results:
            st.warning("⚠️ No component-specific results available.")
            return

        # Get component names and count
        component_names = component_results[0]['component_names']
        n_components_y = len(component_names)

        # Prepare data for plotting
        latent_vars = [result['n_components'] for result in component_results]

        # Create comprehensive multi-component analysis
        st.markdown("##### 🎯 Multi-Component Analysis")
        st.info(f"📊 **Analyzing {n_components_y} components**: {', '.join(component_names)}")

        # Create subplot layout - adjust based on number of components
        if n_components_y == 2:
            rows, cols = 2, 2
            subplot_titles = (
                f'RMSECV vs Latent Variables - {component_names[0]}',
                f'RMSECV vs Latent Variables - {component_names[1]}',
                'Overall RMSECV vs Latent Variables',
                'Component Comparison'
            )
        else:
            # For more than 2 components, create a dynamic layout
            rows = min(3, (n_components_y + 1) // 2 + 1)
            cols = 2
            subplot_titles = []
            for i, name in enumerate(component_names):
                subplot_titles.append(f'RMSECV vs LV - {name}')
            subplot_titles.append('Overall RMSECV')
            if len(subplot_titles) % 2 == 1:
                subplot_titles.append('Component Comparison')

        fig = make_subplots(
            rows=rows, cols=cols,
            subplot_titles=subplot_titles[:rows*cols],
            vertical_spacing=0.12,
            horizontal_spacing=0.1
        )

        # Color palette for components
        colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b']

        # Plot individual component RMSECV curves
        for comp_idx, comp_name in enumerate(component_names):
            # Extract RMSECV values for this component
            comp_rmsecv = [result['component_rmsecv'][comp_idx] for result in component_results]
            comp_r2 = [result['component_r2'][comp_idx] for result in component_results]

            # Find optimal latent variables for this component
            best_idx = np.argmin(comp_rmsecv)
            best_lv = latent_vars[best_idx]
            best_rmsecv = comp_rmsecv[best_idx]

            # Calculate subplot position
            row = (comp_idx // cols) + 1
            col = (comp_idx % cols) + 1

            # Add RMSECV curve for this component
            fig.add_trace(
                go.Scatter(
                    x=latent_vars,
                    y=comp_rmsecv,
                    mode='lines+markers',
                    name=f'{comp_name} RMSECV',
                    line=dict(color=colors[comp_idx % len(colors)], width=3),
                    marker=dict(size=8),
                    hovertemplate=f'{comp_name}<br>Latent Variables: %{{x}}<br>RMSECV: %{{y:.4f}}<extra></extra>',
                    showlegend=True
                ),
                row=row, col=col
            )

            # Highlight optimal point
            fig.add_trace(
                go.Scatter(
                    x=[best_lv],
                    y=[best_rmsecv],
                    mode='markers',
                    name=f'{comp_name} Optimal',
                    marker=dict(color='gold', size=15, symbol='star', line=dict(color='black', width=2)),
                    hovertemplate=f'{comp_name} Optimal<br>Latent Variables: {best_lv}<br>RMSECV: {best_rmsecv:.4f}<extra></extra>',
                    showlegend=False
                ),
                row=row, col=col
            )

        # Add overall RMSECV plot
        overall_rmsecv = df['rmsecv'].values
        overall_r2 = df['r2'].values
        best_overall_idx = np.argmin(overall_rmsecv)
        best_overall_lv = latent_vars[best_overall_idx]
        best_overall_rmsecv = overall_rmsecv[best_overall_idx]

        # Position for overall plot
        overall_row = min(rows, (n_components_y // cols) + 2)
        overall_col = 1

        fig.add_trace(
            go.Scatter(
                x=latent_vars,
                y=overall_rmsecv,
                mode='lines+markers',
                name='Overall RMSECV',
                line=dict(color='red', width=4),
                marker=dict(size=10),
                hovertemplate='Overall<br>Latent Variables: %{x}<br>RMSECV: %{y:.4f}<extra></extra>',
                showlegend=True
            ),
            row=overall_row, col=overall_col
        )

        # Highlight overall optimal point
        fig.add_trace(
            go.Scatter(
                x=[best_overall_lv],
                y=[best_overall_rmsecv],
                mode='markers',
                name='Overall Optimal',
                marker=dict(color='gold', size=20, symbol='star', line=dict(color='black', width=2)),
                hovertemplate=f'Overall Optimal<br>Latent Variables: {best_overall_lv}<br>RMSECV: {best_overall_rmsecv:.4f}<extra></extra>',
                showlegend=False
            ),
            row=overall_row, col=overall_col
        )

        # Add component comparison plot if space allows
        if rows * cols > n_components_y + 1:
            comp_row = overall_row
            comp_col = 2

            # Plot all components on the same chart for comparison
            for comp_idx, comp_name in enumerate(component_names):
                comp_rmsecv = [result['component_rmsecv'][comp_idx] for result in component_results]

                fig.add_trace(
                    go.Scatter(
                        x=latent_vars,
                        y=comp_rmsecv,
                        mode='lines+markers',
                        name=f'{comp_name} (Comparison)',
                        line=dict(color=colors[comp_idx % len(colors)], width=2, dash='dot'),
                        marker=dict(size=6),
                        hovertemplate=f'{comp_name}<br>Latent Variables: %{{x}}<br>RMSECV: %{{y:.4f}}<extra></extra>',
                        showlegend=False
                    ),
                    row=comp_row, col=comp_col
                )

        # Update layout
        fig.update_layout(
            title=f"{algorithm} - Multi-Component Optimization Analysis ({n_components_y} Components)",
            height=300 * rows,
            showlegend=True,
            template="plotly_white",
            legend=dict(
                orientation="h",
                yanchor="bottom",
                y=1.02,
                xanchor="right",
                x=1
            )
        )

        # Update axes labels
        for i in range(1, rows + 1):
            for j in range(1, cols + 1):
                fig.update_xaxes(title_text="Number of Latent Variables", row=i, col=j)
                fig.update_yaxes(title_text="RMSECV", row=i, col=j)

        st.plotly_chart(fig, use_container_width=True)

        # Component-specific optimization summary
        st.markdown("##### 🔍 Component-Specific Optimization Results")

        # Create summary table
        summary_data = []
        for comp_idx, comp_name in enumerate(component_names):
            comp_rmsecv = [result['component_rmsecv'][comp_idx] for result in component_results]
            comp_r2 = [result['component_r2'][comp_idx] for result in component_results]

            best_idx = np.argmin(comp_rmsecv)
            best_lv = latent_vars[best_idx]
            best_rmsecv = comp_rmsecv[best_idx]
            best_r2 = comp_r2[best_idx]

            summary_data.append({
                'Component': comp_name,
                'Optimal Latent Variables': best_lv,
                'Best RMSECV': f"{best_rmsecv:.4f}",
                'Best R²': f"{best_r2:.4f}",
                'RMSECV Range': f"{min(comp_rmsecv):.4f} - {max(comp_rmsecv):.4f}"
            })

        # Add overall summary
        summary_data.append({
            'Component': '**Overall (Average)**',
            'Optimal Latent Variables': best_overall_lv,
            'Best RMSECV': f"{best_overall_rmsecv:.4f}",
            'Best R²': f"{overall_r2[best_overall_idx]:.4f}",
            'RMSECV Range': f"{min(overall_rmsecv):.4f} - {max(overall_rmsecv):.4f}"
        })

        import pandas as pd
        summary_df = pd.DataFrame(summary_data)
        st.dataframe(summary_df, use_container_width=True, hide_index=True)

        # Optimization insights
        st.markdown("##### 💡 Multi-Component Optimization Insights")

        col1, col2, col3 = st.columns(3)

        with col1:
            st.markdown("**🎯 Optimal Configuration:**")
            st.write(f"• **Overall Optimal LV**: {best_overall_lv}")
            st.write(f"• **Overall RMSECV**: {best_overall_rmsecv:.4f}")

            # Check if all components agree on optimal LV
            individual_optimal_lv = []
            for comp_idx in range(n_components_y):
                comp_rmsecv = [result['component_rmsecv'][comp_idx] for result in component_results]
                best_idx = np.argmin(comp_rmsecv)
                individual_optimal_lv.append(latent_vars[best_idx])

            if len(set(individual_optimal_lv)) == 1:
                st.success("✅ All components agree on optimal LV")
            else:
                st.warning(f"⚠️ Components prefer different LV: {individual_optimal_lv}")

        with col2:
            st.markdown("**📊 Component Performance:**")
            # Find best and worst performing components
            final_rmsecv = [result['component_rmsecv'] for result in component_results if result['n_components'] == best_overall_lv][0]
            best_comp_idx = np.argmin(final_rmsecv)
            worst_comp_idx = np.argmax(final_rmsecv)

            st.write(f"• **Best Component**: {component_names[best_comp_idx]}")
            st.write(f"  RMSECV: {final_rmsecv[best_comp_idx]:.4f}")
            st.write(f"• **Most Challenging**: {component_names[worst_comp_idx]}")
            st.write(f"  RMSECV: {final_rmsecv[worst_comp_idx]:.4f}")

        with col3:
            st.markdown("**⚖️ Model Complexity:**")
            # Calculate performance improvement vs complexity
            if len(latent_vars) > 1:
                simple_rmsecv = overall_rmsecv[0]  # 1 LV
                complex_improvement = (simple_rmsecv - best_overall_rmsecv) / simple_rmsecv * 100
                st.write(f"• **Complexity Benefit**: {complex_improvement:.1f}%")
                st.write(f"• **vs 1 Latent Variable**")

                # Efficiency metric
                efficiency = complex_improvement / best_overall_lv
                st.write(f"• **Efficiency**: {efficiency:.2f}%/LV")

        # Recommendation
        st.markdown("##### 🎯 Recommendation")

        # Determine if the optimal choice is reasonable
        if len(set(individual_optimal_lv)) == 1:
            st.success(f"✅ **Recommended**: Use **{best_overall_lv} latent variables** - all components agree on this optimal choice.")
        else:
            st.info(f"ℹ️ **Recommended**: Use **{best_overall_lv} latent variables** for overall best performance, though individual components may prefer different numbers.")

            # Show individual preferences
            for comp_idx, comp_name in enumerate(component_names):
                if individual_optimal_lv[comp_idx] != best_overall_lv:
                    comp_rmsecv = [result['component_rmsecv'][comp_idx] for result in component_results]
                    comp_best_rmsecv = min(comp_rmsecv)
                    overall_comp_rmsecv = [result['component_rmsecv'][comp_idx] for result in component_results if result['n_components'] == best_overall_lv][0]
                    performance_loss = (overall_comp_rmsecv - comp_best_rmsecv) / comp_best_rmsecv * 100
                    st.caption(f"  • {comp_name} prefers {individual_optimal_lv[comp_idx]} LV (performance loss: {performance_loss:.1f}%)")

    def _render_advanced_algorithm_optimization_plots(self, df: pd.DataFrame, algorithm: str) -> None:
        """Render advanced optimization plots for non-PLS algorithms."""
        import plotly.graph_objects as go
        from plotly.subplots import make_subplots

        # Create comprehensive analysis for non-PLS algorithms
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=(
                'Performance Ranking',
                'Parameter Sensitivity',
                'Performance Distribution',
                'Optimization Progress'
            )
        )

        # Sort by RMSECV for ranking
        df_sorted = df.sort_values('rmsecv').reset_index(drop=True)

        # 1. Performance Ranking Plot
        fig.add_trace(
            go.Scatter(
                x=list(range(len(df_sorted))),
                y=df_sorted['rmsecv'],
                mode='lines+markers',
                name='RMSECV Ranking',
                line=dict(color='red', width=3),
                marker=dict(size=8),
                hovertemplate='Rank: %{x}<br>RMSECV: %{y:.4f}<extra></extra>'
            ),
            row=1, col=1
        )

        # Highlight best configuration
        fig.add_trace(
            go.Scatter(
                x=[0],
                y=[df_sorted.iloc[0]['rmsecv']],
                mode='markers',
                name='Best Configuration',
                marker=dict(color='gold', size=20, symbol='star', line=dict(color='black', width=2)),
                showlegend=False
            ),
            row=1, col=1
        )

        # 2. Parameter Sensitivity Analysis
        # Try to identify the most important parameter
        param_columns = [col for col in df.columns if col not in ['rmsecv', 'r2']]
        if param_columns:
            # Use the first parameter for sensitivity analysis
            main_param = param_columns[0]
            if main_param in df.columns:
                # Group by parameter values and show performance
                param_groups = df.groupby(main_param)['rmsecv'].agg(['mean', 'std']).reset_index()

                fig.add_trace(
                    go.Scatter(
                        x=param_groups[main_param],
                        y=param_groups['mean'],
                        mode='lines+markers',
                        name=f'{main_param} Sensitivity',
                        line=dict(color='blue', width=3),
                        marker=dict(size=10),
                        error_y=dict(type='data', array=param_groups['std'], visible=True),
                        hovertemplate=f'{main_param}: %{{x}}<br>Mean RMSECV: %{{y:.4f}}<extra></extra>'
                    ),
                    row=1, col=2
                )

        # 3. Performance Distribution
        fig.add_trace(
            go.Histogram(
                x=df['rmsecv'],
                name='RMSECV Distribution',
                nbinsx=min(20, len(df)//2),
                marker_color='green',
                opacity=0.7
            ),
            row=2, col=1
        )

        # Add vertical line for best performance
        best_rmsecv = df['rmsecv'].min()
        fig.add_vline(x=best_rmsecv, line_dash="dash", line_color="red", row=2, col=1)

        # 4. Optimization Progress (if we can infer order)
        # Show R² vs RMSECV scatter
        fig.add_trace(
            go.Scatter(
                x=df['rmsecv'],
                y=df['r2'],
                mode='markers',
                name='RMSECV vs R²',
                marker=dict(
                    size=12,
                    color=df['rmsecv'],
                    colorscale='RdYlBu_r',
                    showscale=True,
                    colorbar=dict(title="RMSECV", x=1.02)
                ),
                hovertemplate='RMSECV: %{x:.4f}<br>R²: %{y:.4f}<extra></extra>'
            ),
            row=2, col=2
        )

        # Highlight best point
        best_idx = df['rmsecv'].idxmin()
        best_r2 = df.loc[best_idx, 'r2']
        fig.add_trace(
            go.Scatter(
                x=[best_rmsecv],
                y=[best_r2],
                mode='markers',
                name='Optimal Point',
                marker=dict(color='gold', size=20, symbol='star', line=dict(color='black', width=2)),
                showlegend=False
            ),
            row=2, col=2
        )

        # Update layout
        fig.update_layout(
            title=f"{algorithm} - Advanced Parameter Optimization Analysis",
            height=800,
            showlegend=True,
            template="plotly_white"
        )

        # Update axes labels
        fig.update_xaxes(title_text="Configuration Rank", row=1, col=1)
        fig.update_xaxes(title_text=param_columns[0] if param_columns else "Parameter", row=1, col=2)
        fig.update_xaxes(title_text="RMSECV", row=2, col=1)
        fig.update_xaxes(title_text="RMSECV", row=2, col=2)

        fig.update_yaxes(title_text="RMSECV", row=1, col=1)
        fig.update_yaxes(title_text="Mean RMSECV", row=1, col=2)
        fig.update_yaxes(title_text="Frequency", row=2, col=1)
        fig.update_yaxes(title_text="R²", row=2, col=2)

        st.plotly_chart(fig, use_container_width=True)

        # Advanced insights for non-PLS algorithms
        st.markdown("##### 🔍 Advanced Algorithm Analysis")

        col1, col2, col3 = st.columns(3)

        with col1:
            st.markdown("**🎯 Performance Summary:**")
            st.write(f"• **Best RMSECV**: {best_rmsecv:.4f}")
            st.write(f"• **Best R²**: {best_r2:.4f}")
            st.write(f"• **Configurations Tested**: {len(df)}")

            # Performance spread
            rmsecv_range = df['rmsecv'].max() - df['rmsecv'].min()
            st.write(f"• **Performance Range**: {rmsecv_range:.4f}")

        with col2:
            st.markdown("**📊 Parameter Analysis:**")
            if param_columns:
                # Find most sensitive parameter
                sensitivities = {}
                for param in param_columns:
                    if param in df.columns and df[param].nunique() > 1:
                        param_groups = df.groupby(param)['rmsecv'].agg(['mean', 'std'])
                        sensitivity = param_groups['mean'].max() - param_groups['mean'].min()
                        sensitivities[param] = sensitivity

                if sensitivities:
                    most_sensitive = max(sensitivities, key=sensitivities.get)
                    st.write(f"• **Most Sensitive**: {most_sensitive}")
                    st.write(f"• **Sensitivity Range**: {sensitivities[most_sensitive]:.4f}")

                    # Best parameter value
                    best_params = df.loc[best_idx]
                    for param in param_columns[:3]:  # Show top 3 parameters
                        if param in best_params:
                            st.write(f"• **Best {param}**: {best_params[param]}")

        with col3:
            st.markdown("**🎲 Optimization Quality:**")
            # Calculate optimization efficiency
            theoretical_best = df['rmsecv'].min()
            theoretical_worst = df['rmsecv'].max()
            if theoretical_worst > theoretical_best:
                efficiency = (theoretical_worst - theoretical_best) / theoretical_worst * 100
                st.write(f"• **Optimization Efficiency**: {efficiency:.1f}%")

            # Consistency analysis
            rmsecv_std = df['rmsecv'].std()
            rmsecv_mean = df['rmsecv'].mean()
            cv_coefficient = (rmsecv_std / rmsecv_mean) * 100
            st.write(f"• **Performance Consistency**: {100-cv_coefficient:.1f}%")

            # Recommendation
            if cv_coefficient < 10:
                st.success("✅ Stable optimization")
            elif cv_coefficient < 20:
                st.warning("⚠️ Moderate variability")
            else:
                st.error("❌ High variability")

    def _render_performance_distribution(self, df: pd.DataFrame) -> None:
        """Render performance distribution plots."""
        st.markdown("##### 📊 Performance Distribution Analysis")

        import plotly.graph_objects as go
        from plotly.subplots import make_subplots

        fig = make_subplots(
            rows=1, cols=2,
            subplot_titles=('RMSECV Distribution', 'R² Distribution')
        )

        # RMSECV histogram
        fig.add_trace(
            go.Histogram(
                x=df['rmsecv'],
                name='RMSECV',
                nbinsx=20,
                marker_color='red',
                opacity=0.7
            ),
            row=1, col=1
        )

        # R² histogram
        fig.add_trace(
            go.Histogram(
                x=df['r2'],
                name='R²',
                nbinsx=20,
                marker_color='blue',
                opacity=0.7
            ),
            row=1, col=2
        )

        # Add vertical lines for best values
        best_rmsecv = df['rmsecv'].min()
        best_r2 = df['r2'].max()

        fig.add_vline(x=best_rmsecv, line_dash="dash", line_color="red", row=1, col=1)
        fig.add_vline(x=best_r2, line_dash="dash", line_color="blue", row=1, col=2)

        fig.update_layout(
            title="Performance Distribution Across All Configurations",
            height=400,
            showlegend=False,
            template="plotly_white"
        )

        fig.update_xaxes(title_text="RMSECV", row=1, col=1)
        fig.update_xaxes(title_text="R²", row=1, col=2)
        fig.update_yaxes(title_text="Frequency", row=1, col=1)
        fig.update_yaxes(title_text="Frequency", row=1, col=2)

        st.plotly_chart(fig, use_container_width=True)

        # Performance statistics
        col1, col2 = st.columns(2)
        with col1:
            st.markdown("**RMSECV Statistics:**")
            st.write(f"• **Best**: {df['rmsecv'].min():.4f}")
            st.write(f"• **Worst**: {df['rmsecv'].max():.4f}")
            st.write(f"• **Mean**: {df['rmsecv'].mean():.4f}")
            st.write(f"• **Std**: {df['rmsecv'].std():.4f}")

        with col2:
            st.markdown("**R² Statistics:**")
            st.write(f"• **Best**: {df['r2'].max():.4f}")
            st.write(f"• **Worst**: {df['r2'].min():.4f}")
            st.write(f"• **Mean**: {df['r2'].mean():.4f}")
            st.write(f"• **Std**: {df['r2'].std():.4f}")

    def _render_manual_results_display(self, results: Dict[str, Any]) -> None:
        """Render manual optimization results display."""
        st.markdown("#### 🧠 Manual Configuration Results")

        model_params = results.get("model_params", {})
        if model_params:
            st.markdown("**Parameters Used:**")
            for param, value in model_params.items():
                st.write(f"• **{param}**: {value}")

        cv_info = {
            "CV Folds": results.get("cv_folds", "N/A"),
            "CV Method": results.get("cv_method", "N/A")
        }

        st.markdown("**Cross-Validation Configuration:**")
        for param, value in cv_info.items():
            st.write(f"• **{param}**: {value}")

    def _render_stage5_results_visualization(self) -> None:
        """Render Stage 5: Results Visualization."""
        if not self.session.has("model_training_results"):
            st.error("❌ No training results found. Please complete Stage 4 first.")
            if st.button("🔙 Back to Optimization", key="back_to_stage4"):
                self.session.set("model_selection_stage", 4)
                st.rerun()
            return

        results = self.session.get("model_training_results")
        optimization_route = results["optimization_route"]
        algorithm = results["algorithm"]

        col1, col2 = st.columns([4, 1])
        with col1:
            st.markdown("### 📊 Stage 5: Results Visualization")
            st.markdown("*Comprehensive analysis of model performance and optimization results*")
        with col2:
            # Create contextual prompt for ChatGPT help
            if optimization_route == "manual":
                rmsecv = results["results"]["rmsecv"]
                train_r2 = results["results"]["train_r2"]
                model_params = results["model_params"]

                # Build parameters string
                params_str = ", ".join([f"{param}: {value}" for param, value in model_params.items()])

                contextual_prompt = f"""Please help me interpret the model training results for chemometrics:

**Algorithm Used**: {algorithm}
**Optimization Approach**: Manual Parameter Configuration
**Performance Results**:
- RMSECV: {rmsecv:.4f}
- Training R²: {train_r2:.4f}

**Parameters Used**: {params_str}

Please help me understand:
1. What does the RMSECV value of {rmsecv:.4f} indicate about the model performance?
2. How should I interpret the Training R² value of {train_r2:.4f}?
3. Are these results suitable for chemometric analysis?
4. How do the parameters I chose affect the model performance?
5. What are the next steps I should take in my chemometric workflow?
6. Are there any potential improvements or concerns based on these results?

Please provide practical guidance for interpreting these results and recommendations for proceeding with the analysis."""

            else:  # automatic
                best_rmsecv = results["results"]["best_rmsecv"]
                train_r2 = results["results"]["train_r2"]
                best_params = results["results"]["best_params"]
                optimization_metric = results["optimization_metric"]

                # Build best parameters string
                best_params_str = ", ".join([f"{param}: {value}" for param, value in best_params.items()])

                contextual_prompt = f"""Please help me interpret the grid search optimization results for chemometrics:

**Algorithm Used**: {algorithm}
**Optimization Approach**: Automated Grid Search
**Optimization Results**:
- Best RMSECV achieved: {best_rmsecv:.4f}
- Training R² achieved: {train_r2:.4f}
- Optimization metric: {optimization_metric}

**Best Parameters Found**: {best_params_str}

Please help me understand:
1. What does the best RMSECV value of {best_rmsecv:.4f} indicate about the model performance?
2. How should I interpret the Training R² value of {train_r2:.4f}?
3. How should I interpret the optimal parameters found during grid search?
4. Is this model performance suitable for chemometric analysis?
5. What are the next steps I should take in my chemometric workflow?
6. Are there any potential concerns or recommendations based on these results?

Please provide practical guidance for interpreting these optimization results and recommendations for proceeding with the analysis."""

            ChatGPTHelper.create_help_icon(
                f"{algorithm} Results Interpretation",
                f"interpreting {optimization_route} optimization results for {algorithm.lower()}",
                contextual_prompt
            )

        # Results summary
        st.markdown("#### 🏆 Model Training Results Summary")

        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Optimization Route", "Manual" if optimization_route == "manual" else "Automated")
        with col2:
            st.metric("Algorithm", algorithm)
        with col3:
            if optimization_route == "manual":
                st.metric("RMSECV", f"{results['results']['rmsecv']:.4f}")
            else:
                st.metric("Best RMSECV", f"{results['results']['best_rmsecv']:.4f}")
        with col4:
            if optimization_route == "manual":
                st.metric("Train R²", f"{results['results']['train_r2']:.4f}")
            else:
                st.metric("Train R²", f"{results['results']['train_r2']:.4f}")

        # Route-specific results display
        if optimization_route == "automatic":
            self._render_grid_search_results_display(results)
        else:
            self._render_manual_results_display(results)

        # Model performance visualization
        st.markdown("#### 📈 Model Performance Visualization")
        self._render_model_performance_plots(results)

        # Navigation buttons
        st.markdown("---")
        col1, col2, col3 = st.columns([1, 2, 1])

        with col1:
            if st.button("🔙 Back to Optimization", key="back_to_optimization"):
                self.session.set("model_selection_stage", 4)
                st.rerun()

        with col3:
            if st.button("➡️ Next: Build Model", type="primary", key="proceed_to_build_model"):
                # Move to Step 6: Build Model
                self.session.set("current_step", 6)
                st.rerun()

    def _render_model_application_interface(self) -> None:
        """Render model application interface for final configuration."""
        if not self.session.has("model_training_results"):
            st.error("❌ No training results found. Please complete previous stages first.")
            return

        results = self.session.get("model_training_results")

        # Show current model summary
        st.markdown("#### 📋 Final Model Configuration Summary")

        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Optimization Route", "Manual" if results["optimization_route"] == "manual" else "Automated")
        with col2:
            st.metric("Algorithm", results["algorithm"])
        with col3:
            if results["optimization_route"] == "manual":
                st.metric("RMSECV", f"{results['results']['rmsecv']:.4f}")
            else:
                st.metric("Best RMSECV", f"{results['results']['best_rmsecv']:.4f}")
        with col4:
            if results["optimization_route"] == "manual":
                st.metric("Train R²", f"{results['results']['train_r2']:.4f}")
            else:
                st.metric("Train R²", f"{results['results']['train_r2']:.4f}")

        # Apply model button
        st.markdown("#### ✅ Apply Model Configuration")

        # Enhanced application description
        st.markdown("""
        <div style="
            padding: 1rem 1.5rem;
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            border-left: 5px solid #4CAF50;
            border-radius: 8px;
            margin: 1rem 0;
            font-family: 'Nunito Sans', sans-serif;
            box-shadow: 0 2px 4px rgba(76, 175, 80, 0.1);
        ">
            <div style="
                color: #2E7D32;
                font-weight: 600;
                font-size: 1rem;
                line-height: 1.4;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            ">
                <span style="font-size: 1.2rem;">🎯</span>
                Apply the selected model configuration for prediction on unknown samples and integration with the chemometric workflow.
            </div>
        </div>
        """, unsafe_allow_html=True)

        # Centered apply button
        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            if st.button("✅ Apply Model Configuration", type="primary", key="apply_final_model_configuration", use_container_width=True):
                try:
                    # Store final model configuration
                    self.session.set("final_model_config", results)
                    self.session.set("model_selection_applied", True)

                    st.success("✅ Model configuration applied successfully!")
                    st.info("🚀 You can now proceed to prediction/validation steps.")
                    st.rerun()

                except Exception as e:
                    st.error(f"Error applying model configuration: {str(e)}")

        # Show application status
        if self.session.has("model_selection_applied") and self.session.get("model_selection_applied"):
            # Enhanced success message
            st.markdown("""
            <div style="
                padding: 1rem 1.5rem;
                background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
                border-left: 5px solid #4CAF50;
                border-radius: 8px;
                margin: 1rem 0;
                font-family: 'Nunito Sans', sans-serif;
                box-shadow: 0 2px 4px rgba(76, 175, 80, 0.1);
            ">
                <div style="
                    color: #2E7D32;
                    font-weight: 600;
                    font-size: 1rem;
                    line-height: 1.4;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                ">
                    <span style="font-size: 1.2rem;">✅</span>
                    Model configuration has been applied! Ready for prediction and validation.
                </div>
            </div>
            """, unsafe_allow_html=True)

            # Show next steps
            st.markdown("#### 🚀 Next Steps")
            # Enhanced next steps container
            st.markdown("""
            <div style="
                padding: 1.5rem;
                background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
                border-left: 5px solid #1976D2;
                border-radius: 8px;
                margin: 1rem 0;
                font-family: 'Nunito Sans', sans-serif;
                box-shadow: 0 2px 4px rgba(25, 118, 210, 0.1);
            ">
                <div style="color: #1565C0; font-weight: 600; font-size: 1.1rem; margin-bottom: 1rem;">
                    <span style="font-size: 1.3rem;">🚀</span> Your model is now ready for:
                </div>
                <div style="color: #1976D2; font-size: 0.95rem; line-height: 1.6;">
                    <div style="margin: 0.5rem 0; display: flex; align-items: center; gap: 0.5rem;">
                        <span style="font-size: 1.1rem;">🎯</span> Prediction on unknown samples
                    </div>
                    <div style="margin: 0.5rem 0; display: flex; align-items: center; gap: 0.5rem;">
                        <span style="font-size: 1.1rem;">📊</span> Model validation with test data
                    </div>
                    <div style="margin: 0.5rem 0; display: flex; align-items: center; gap: 0.5rem;">
                        <span style="font-size: 1.1rem;">📈</span> Performance evaluation and comparison
                    </div>
                    <div style="margin: 0.5rem 0; display: flex; align-items: center; gap: 0.5rem;">
                        <span style="font-size: 1.1rem;">📋</span> Report generation and documentation
                    </div>
                </div>
            </div>
            """, unsafe_allow_html=True)
        else:
            # Enhanced warning message
            st.markdown("""
            <div style="
                padding: 1rem 1.5rem;
                background: linear-gradient(135deg, #fff3e0 0%, #ffcc80 100%);
                border-left: 5px solid #FF9800;
                border-radius: 8px;
                margin: 1rem 0;
                font-family: 'Nunito Sans', sans-serif;
                box-shadow: 0 2px 4px rgba(255, 152, 0, 0.1);
            ">
                <div style="
                    color: #E65100;
                    font-weight: 600;
                    font-size: 1rem;
                    line-height: 1.4;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                ">
                    <span style="font-size: 1.2rem;">⚠️</span>
                    Model configuration not yet applied. Click the button above to apply.
                </div>
            </div>
            """, unsafe_allow_html=True)

        # Navigation buttons
        st.markdown("---")
        col1, col2, col3 = st.columns([1, 2, 1])

        with col1:
            if st.button("🔙 Back to Results", key="back_to_results"):
                self.session.set("model_selection_stage", 5)
                st.rerun()

        with col3:
            if self.session.has("model_selection_applied") and self.session.get("model_selection_applied"):
                if st.button("➡️ Proceed to Build Model", type="primary", key="proceed_to_step6"):
                    # Navigate to Step 6 (Step 5 completion is tracked by model_selection_applied flag)
                    self.session.set_current_step(6)
                    st.success("🎉 Proceeding to Step 6: Build Model")
                    st.rerun()
            else:
                st.button("✅ Apply Model First", key="apply_model_first", disabled=True)

    def _render_route_selection(self) -> None:
        """Render the route selection interface."""
        st.markdown("#### 🛤️ Choose Your Model Selection Route")
        st.markdown("Select your preferred approach for model configuration and optimization:")

        col1, col2 = st.columns(2)

        # Manual Configuration Route
        with col1:
            st.markdown("""
            <div style="
                padding: 1.5rem;
                border: 3px solid #2E8B57;
                background: linear-gradient(135deg, #f0fff0 0%, #e6ffe6 100%);
                border-radius: 15px;
                text-align: center;
                margin-bottom: 1rem;
                height: 280px;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
            ">
                <div>
                    <h3 style="color: #2E8B57; margin: 0; font-size: 1.4rem;">
                        🧠 Manual Model Configuration
                    </h3>
                    <p style="color: #228B22; margin: 0.5rem 0; font-size: 1rem; font-weight: 500;">
                        Expert Parameter Control
                    </p>
                    <div style="color: #2E8B57; font-size: 0.9rem; text-align: left; margin: 1rem 0;">
                        <p><strong>✨ Perfect for:</strong></p>
                        <ul style="margin: 0; padding-left: 1.2rem;">
                            <li>Domain experts with model knowledge</li>
                            <li>Specific parameter requirements</li>
                            <li>Custom model configurations</li>
                            <li>Quick single-model testing</li>
                        </ul>
                    </div>
                </div>
            </div>
            """, unsafe_allow_html=True)

            if st.button("🧠 Choose Manual Configuration Route", key="select_manual_route", use_container_width=True):
                self.session.set("selected_model_route", "manual")
                st.rerun()

        # Automatic Grid Search Route
        with col2:
            st.markdown("""
            <div style="
                padding: 1.5rem;
                border: 3px solid #4169E1;
                background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
                border-radius: 15px;
                text-align: center;
                margin-bottom: 1rem;
                height: 280px;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
            ">
                <div>
                    <h3 style="color: #4169E1; margin: 0; font-size: 1.4rem;">
                        🤖 Automatic Grid Search
                    </h3>
                    <p style="color: #1E90FF; margin: 0.5rem 0; font-size: 1rem; font-weight: 500;">
                        Automated Optimization
                    </p>
                    <div style="color: #4169E1; font-size: 0.9rem; text-align: left; margin: 1rem 0;">
                        <p><strong>🚀 Perfect for:</strong></p>
                        <ul style="margin: 0; padding-left: 1.2rem;">
                            <li>Automated parameter optimization</li>
                            <li>Multiple model comparison</li>
                            <li>Comprehensive grid search</li>
                            <li>Performance-driven selection</li>
                        </ul>
                    </div>
                </div>
            </div>
            """, unsafe_allow_html=True)

            if st.button("🤖 Choose Automatic Grid Search Route", key="select_automatic_route", use_container_width=True):
                self.session.set("selected_model_route", "automatic")
                st.rerun()

        st.markdown("---")

    def _render_manual_configuration_route(self, x_train: pd.DataFrame, y_train: pd.DataFrame) -> None:
        """Render manual model configuration route."""
        # Manual route header with styling
        st.markdown("""
        <div style="
            padding: 1rem;
            border-left: 5px solid #2E8B57;
            background: linear-gradient(90deg, #f0fff0 0%, #ffffff 100%);
            border-radius: 0 10px 10px 0;
            margin-bottom: 1.5rem;
        ">
            <h4 style="color: #2E8B57; margin: 0; font-size: 1.3rem;">
                🧠 Manual Model Configuration Route
            </h4>
            <p style="color: #228B22; margin: 0.5rem 0 0 0; font-size: 1rem;">
                🔬 Configure model parameters manually with expert control
            </p>
        </div>
        """, unsafe_allow_html=True)

        # Model selection
        self._render_model_selection_interface(x_train, y_train, "manual")

    def _render_automatic_grid_search_route(self, x_train: pd.DataFrame, y_train: pd.DataFrame) -> None:
        """Render automatic grid search route."""
        # Automatic route header with styling
        st.markdown("""
        <div style="
            padding: 1rem;
            border-left: 5px solid #4169E1;
            background: linear-gradient(90deg, #f0f8ff 0%, #ffffff 100%);
            border-radius: 0 10px 10px 0;
            margin-bottom: 1.5rem;
        ">
            <h4 style="color: #4169E1; margin: 0; font-size: 1.3rem;">
                🤖 Automatic Grid Search Route
            </h4>
            <p style="color: #1E90FF; margin: 0.5rem 0 0 0; font-size: 1rem;">
                🔬 Automated parameter optimization across multiple models
            </p>
        </div>
        """, unsafe_allow_html=True)

        # Model selection
        self._render_model_selection_interface(x_train, y_train, "automatic")

    def _render_model_selection_interface(self, x_train: pd.DataFrame, y_train: pd.DataFrame, route_type: str) -> None:
        """Render model selection interface based on route type."""
        # Model selection dropdown
        st.markdown("##### 🤖 Select Chemometric Model")

        available_models = {
            "NIPALS (PLS1, PLS2)": "Authentic NIPALS implementation; iterative deflation algorithm; handles missing data well; supports both single and multiple response variables",
            "SIMPLS": "Computationally efficient PLS algorithm; good orthogonality properties; faster than NIPALS for large datasets",
            "Enhanced ANN (MATLAB-style)": "MATLAB-style neural network with PCA integration, custom data division, and proper normalization; ideal for chemometric applications",
            "Multilayer Perceptron (MLP)": "Neural network with multiple hidden layers; excellent for non-linear relationships; requires more data",
            "ε-Support Vector Regression (ε-SVR)": "Robust to outliers; works well with high-dimensional data; good generalization capability",
            "Nu-SVR": "Alternative to ε-SVR using ν parameter; automatic selection of support vectors; good for noisy data",
            "XGBoost": "Gradient boosting ensemble method; excellent performance; handles missing values; feature importance available" if HAS_XGBOOST else "XGBoost (Not Available - Install with: pip install xgboost)"
        }

        # Filter available models based on installation
        if not HAS_XGBOOST:
            available_models = {k: v for k, v in available_models.items() if "XGBoost" not in k}

        selected_model = st.selectbox(
            "Choose Model Algorithm:",
            list(available_models.keys()),
            help="Select the chemometric model algorithm to use"
        )

        st.info(f"**{selected_model}**: {available_models[selected_model]}")

        # Model-specific parameters based on route type
        if route_type == "manual":
            st.markdown("##### ⚙️ Model Parameters")
            model_params = self._render_model_specific_parameters(selected_model, x_train)

            # Cross-validation settings
            st.markdown("##### 🔄 Cross-Validation Settings")
            col1, col2 = st.columns(2)

            with col1:
                cv_folds = st.slider(
                    "Cross-Validation Folds",
                    min_value=3,
                    max_value=10,
                    value=5,
                    help="Number of CV folds for evaluation"
                )

            with col2:
                cv_method = st.selectbox(
                    "CV Method",
                    ["K-Fold", "Stratified K-Fold"],
                    help="Cross-validation method"
                )

            # Run model button
            st.markdown("##### 🚀 Run Model")

            if st.button("🚀 Train Model", type="primary", key="train_manual_model"):
                with st.spinner(f"Training {selected_model} with manual parameters..."):
                    try:
                        # Convert to numpy arrays
                        x_array = x_train.select_dtypes(include=[np.number]).values
                        y_array = y_train.select_dtypes(include=[np.number]).values

                        if y_array.ndim == 2 and y_array.shape[1] == 1:
                            y_array = y_array.ravel()

                        # Create CV configuration
                        cv_config = {
                            "method": cv_method,
                            "params": {"n_splits": cv_folds},
                            "mode": "manual"
                        }

                        # Train model with manual parameters using the proper CV function
                        results = self._train_model_with_cv(x_array, y_array, selected_model, model_params, cv_config)

                        # Store results
                        self.session.set("model_selection_results", {
                            "route_type": "manual",
                            "selected_model": selected_model,
                            "model_params": model_params,
                            "cv_folds": cv_folds,
                            "results": results
                        })

                        st.success(f"✅ Model training complete! RMSECV: {results['rmsecv']:.4f}, R²: {results['r2']:.4f}")
                        st.rerun()

                    except Exception as e:
                        st.error(f"Error training model: {str(e)}")

        elif route_type == "automatic":
            # Grid search configuration
            st.markdown("##### 🔍 Grid Search Configuration")

            col1, col2, col3 = st.columns(3)

            with col1:
                cv_folds = st.slider(
                    "Cross-Validation Folds",
                    min_value=3,
                    max_value=10,
                    value=5,
                    help="Number of CV folds for evaluation"
                )

            with col2:
                optimization_metric = st.selectbox(
                    "Optimization Metric",
                    ["RMSECV", "R²", "Combined Score"],
                    help="Metric to optimize during grid search"
                )

            with col3:
                n_jobs = st.slider(
                    "Parallel Jobs",
                    min_value=1,
                    max_value=4,
                    value=2,
                    help="Number of parallel jobs for grid search"
                )

            # Show grid search info
            self._show_model_grid_search_info(selected_model)

            # Run grid search button
            st.markdown("##### 🚀 Run Grid Search")

            if st.button("🚀 Run Grid Search Optimization", type="primary", key="run_grid_search_model"):
                with st.spinner(f"Running grid search optimization for {selected_model}... This may take several minutes."):
                    try:
                        # Convert to numpy arrays
                        x_array = x_train.select_dtypes(include=[np.number]).values
                        y_array = y_train.select_dtypes(include=[np.number]).values

                        if y_array.ndim == 2 and y_array.shape[1] == 1:
                            y_array = y_array.ravel()

                        # Run grid search
                        results = self._run_model_grid_search(
                            x_array, y_array, selected_model, cv_folds, optimization_metric, n_jobs
                        )

                        # Store results
                        self.session.set("model_selection_results", {
                            "route_type": "automatic",
                            "selected_model": selected_model,
                            "cv_folds": cv_folds,
                            "optimization_metric": optimization_metric,
                            "results": results
                        })

                        st.success(f"✅ Grid search complete! Best RMSECV: {results['best_rmsecv']:.4f}, Best R²: {results['best_r2']:.4f}")
                        st.rerun()

                    except Exception as e:
                        st.error(f"Error during grid search: {str(e)}")

    def _render_model_specific_parameters(self, model: str, x_train: pd.DataFrame) -> Dict[str, Any]:
        """Render model-specific parameter controls."""
        params = {}

        if model in ["NIPALS (PLS1, PLS2)", "SIMPLS"]:
            # PLS parameters
            col1, col2 = st.columns(2)
            with col1:
                params['n_components'] = st.slider(
                    "Number of Components",
                    min_value=1,
                    max_value=min(15, x_train.shape[1], x_train.shape[0]-2),
                    value=min(self.session.get("recommended_components", 5), x_train.shape[1]//2),
                    help="Number of PLS components"
                )
            with col2:
                if model == "NIPALS (PLS1, PLS2)":
                    params['mode'] = st.selectbox("PLS Mode", ["PLS1", "PLS2"], help="PLS1 for single response, PLS2 for multiple responses")
                    params['max_iter'] = st.slider("Max Iterations", 100, 1000, 500, help="Maximum iterations for NIPALS algorithm")
                    params['tol'] = st.select_slider("Tolerance", options=[1e-6, 1e-5, 1e-4, 1e-3], value=1e-6, help="Convergence tolerance")

        elif model == "Multilayer Perceptron (MLP)":
            # MLP parameters
            col1, col2 = st.columns(2)
            with col1:
                params['hidden_layer_sizes'] = st.selectbox(
                    "Hidden Layer Architecture",
                    [(50,), (100,), (50, 50), (100, 50), (100, 100), (100, 50, 25)],
                    index=1,
                    help="Architecture of hidden layers"
                )
                params['activation'] = st.selectbox("Activation Function", ['relu', 'tanh', 'logistic'], help="Activation function for hidden layers")
                params['solver'] = st.selectbox("Solver", ['adam', 'lbfgs', 'sgd'], help="Solver for weight optimization")
            with col2:
                params['alpha'] = st.select_slider("Regularization (α)", options=[0.0001, 0.001, 0.01, 0.1], value=0.0001, help="L2 penalty parameter")
                params['learning_rate'] = st.selectbox("Learning Rate", ['constant', 'invscaling', 'adaptive'], help="Learning rate schedule")
                params['max_iter'] = st.slider("Max Iterations", 100, 1000, 200, help="Maximum iterations")

        elif model == "Backpropagation Neural Network (BPNN)":
            # BPNN parameters (similar to MLP but with specific settings)
            col1, col2 = st.columns(2)
            with col1:
                params['hidden_layer_sizes'] = st.selectbox(
                    "Hidden Layer Architecture",
                    [(10,), (20,), (10, 10), (20, 10), (30, 20, 10)],
                    index=2,
                    help="Architecture of hidden layers for BPNN"
                )
                params['learning_rate_init'] = st.select_slider("Initial Learning Rate", options=[0.001, 0.01, 0.1, 0.2], value=0.01)
            with col2:
                params['momentum'] = st.slider("Momentum", 0.0, 1.0, 0.9, help="Momentum for gradient descent")
                params['max_iter'] = st.slider("Max Iterations", 100, 500, 200, help="Maximum iterations")

        elif model == "ε-Support Vector Regression (ε-SVR)":
            # SVR parameters
            col1, col2 = st.columns(2)
            with col1:
                params['C'] = st.select_slider("Regularization (C)", options=[0.1, 1.0, 10.0, 100.0], value=1.0, help="Regularization parameter")
                params['epsilon'] = st.select_slider("Epsilon", options=[0.01, 0.1, 0.2, 0.5], value=0.1, help="Epsilon in epsilon-SVR model")
                params['kernel'] = st.selectbox("Kernel", ['rbf', 'linear', 'poly', 'sigmoid'], help="Kernel type")
            with col2:
                if params['kernel'] in ['rbf', 'poly', 'sigmoid']:
                    params['gamma'] = st.selectbox("Gamma", ['scale', 'auto'], help="Kernel coefficient")
                if params['kernel'] == 'poly':
                    params['degree'] = st.slider("Polynomial Degree", 2, 5, 3, help="Degree for polynomial kernel")

        elif model == "Nu-SVR":
            # Nu-SVR parameters
            col1, col2 = st.columns(2)
            with col1:
                params['C'] = st.select_slider("Regularization (C)", options=[0.1, 1.0, 10.0, 100.0], value=1.0, help="Regularization parameter")
                params['nu'] = st.slider("Nu", 0.01, 1.0, 0.5, help="Upper bound on fraction of training errors")
                params['kernel'] = st.selectbox("Kernel", ['rbf', 'linear', 'poly', 'sigmoid'], help="Kernel type")
            with col2:
                if params['kernel'] in ['rbf', 'poly', 'sigmoid']:
                    params['gamma'] = st.selectbox("Gamma", ['scale', 'auto'], help="Kernel coefficient")

        elif model == "XGBoost" and HAS_XGBOOST:
            # XGBoost parameters
            col1, col2 = st.columns(2)
            with col1:
                params['n_estimators'] = st.slider("Number of Estimators", 50, 500, 100, help="Number of boosting rounds")
                params['max_depth'] = st.slider("Max Depth", 3, 10, 6, help="Maximum tree depth")
                params['learning_rate'] = st.select_slider("Learning Rate", options=[0.01, 0.1, 0.2, 0.3], value=0.1, help="Boosting learning rate")
            with col2:
                params['subsample'] = st.slider("Subsample", 0.5, 1.0, 0.8, help="Subsample ratio of training instances")
                params['colsample_bytree'] = st.slider("Feature Subsample", 0.5, 1.0, 0.8, help="Subsample ratio of features")
                params['reg_alpha'] = st.select_slider("L1 Regularization", options=[0, 0.01, 0.1, 1.0], value=0, help="L1 regularization term")
                params['reg_lambda'] = st.select_slider("L2 Regularization", options=[0, 0.01, 0.1, 1.0], value=1.0, help="L2 regularization term")

        return params

    def _show_model_grid_search_info(self, model: str) -> None:
        """Show grid search information for the selected model."""
        st.info("🔍 **Automatic Grid Search**: Will test multiple parameter combinations to find optimal settings")

        grid_info = {
            "NIPALS (PLS1, PLS2)": {
                "parameters": ["Components: [1, 2, 3, 5, 8, 10]", "Mode: [PLS1, PLS2]", "Max Iterations: [500, 1000]", "Tolerance: [1e-6, 1e-5, 1e-4]"],
                "total": 48
            },
            "SIMPLS": {
                "parameters": ["Components: [1, 2, 3, 5, 8, 10, 15]"],
                "total": 7
            },
            "Multilayer Perceptron (MLP)": {
                "parameters": ["Hidden Layers: [(50,), (100,), (50,50), (100,50)]", "Activation: [relu, tanh]", "Alpha: [0.0001, 0.001, 0.01]", "Solver: [adam, lbfgs]"],
                "total": 24
            },

            "ε-Support Vector Regression (ε-SVR)": {
                "parameters": ["C: [0.1, 1.0, 10.0, 100.0]", "Epsilon: [0.01, 0.1, 0.2]", "Kernel: [rbf, linear, poly]", "Gamma: [scale, auto]"],
                "total": 24
            },
            "Nu-SVR": {
                "parameters": ["C: [0.1, 1.0, 10.0]", "Nu: [0.1, 0.3, 0.5, 0.7]", "Kernel: [rbf, linear]"],
                "total": 24
            },
            "XGBoost": {
                "parameters": ["N_estimators: [50, 100, 200]", "Max_depth: [3, 6, 9]", "Learning_rate: [0.01, 0.1, 0.2]", "Subsample: [0.8, 1.0]"],
                "total": 54
            }
        }

        if model in grid_info:
            info = grid_info[model]
            with st.expander(f"📊 {model} - Grid Search Details"):
                st.markdown("**Parameter Ranges to Test:**")
                for param in info["parameters"]:
                    st.write(f"• {param}")
                st.write(f"• **Total Combinations**: {info['total']} configurations")

    def _train_single_model(self, X: np.ndarray, y: np.ndarray, model_name: str, params: Dict[str, Any], cv_folds: int) -> Dict[str, Any]:
        """Train a single model with given parameters."""
        kf = KFold(n_splits=cv_folds, shuffle=True, random_state=42)

        # Create model based on type
        if model_name in ["NIPALS (PLS1, PLS2)"]:
            model = NIPALSRegression(
                n_components=params['n_components'],
                mode=params.get('mode', 'PLS1'),
                max_iter=params.get('max_iter', 500),
                tol=params.get('tol', 1e-6)
            )
        elif model_name == "SIMPLS":
            model = PLSRegression(n_components=params['n_components'])
        elif model_name == "Enhanced ANN (MATLAB-style)":
            model = EnhancedANN(
                hidden_layer_sizes=params['hidden_layer_sizes'],
                activation=params['activation'],
                solver=params['solver'],
                max_iter=params['max_iter'],
                data_division=params.get('data_division', 'sequential'),
                train_ratio=params.get('train_ratio', 0.48),
                val_ratio=params.get('val_ratio', 0.16),
                test_ratio=params.get('test_ratio', 0.36),
                early_stopping=params.get('early_stopping', True),
                random_state=42
            )
        elif model_name == "Multilayer Perceptron (MLP)":
            model = MLPRegressor(
                hidden_layer_sizes=params['hidden_layer_sizes'],
                activation=params['activation'],
                solver=params['solver'],
                alpha=params['alpha'],
                learning_rate=params['learning_rate'],
                max_iter=params['max_iter'],
                random_state=42
            )
        elif model_name == "Backpropagation Neural Network (BPNN)":
            model = MLPRegressor(
                hidden_layer_sizes=params['hidden_layer_sizes'],
                learning_rate_init=params['learning_rate_init'],
                momentum=params['momentum'],
                max_iter=params['max_iter'],
                solver='sgd',
                random_state=42
            )
        elif model_name == "ε-Support Vector Regression (ε-SVR)":
            model = SVR(
                C=params['C'],
                epsilon=params['epsilon'],
                kernel=params['kernel'],
                gamma=params.get('gamma', 'scale'),
                degree=params.get('degree', 3)
            )
        elif model_name == "Nu-SVR":
            model = NuSVR(
                C=params['C'],
                nu=params['nu'],
                kernel=params['kernel'],
                gamma=params.get('gamma', 'scale')
            )
        elif model_name == "XGBoost" and HAS_XGBOOST:
            import xgboost as xgb
            model = xgb.XGBRegressor(
                n_estimators=params['n_estimators'],
                max_depth=params['max_depth'],
                learning_rate=params['learning_rate'],
                subsample=params['subsample'],
                colsample_bytree=params['colsample_bytree'],
                reg_alpha=params['reg_alpha'],
                reg_lambda=params['reg_lambda'],
                random_state=42
            )
        else:
            raise ValueError(f"Unknown model: {model_name}")

        # Cross-validation
        scores = cross_val_score(model, X, y, cv=kf, scoring='neg_mean_squared_error')
        rmsecv = np.sqrt(-scores.mean())
        r2_scores = cross_val_score(model, X, y, cv=kf, scoring='r2')
        r2 = r2_scores.mean()

        # Train final model on full data
        model.fit(X, y)

        return {
            'model': model,
            'rmsecv': rmsecv,
            'r2': r2,
            'cv_scores': scores,
            'r2_scores': r2_scores,
            'model_name': model_name,
            'parameters': params
        }

    def _show_model_grid_search_info(self, model: str) -> None:
        """Show grid search information for the selected model."""
        st.info("🔍 **Automatic Grid Search**: Will test multiple parameter combinations to find optimal settings")

        grid_info = {
            "NIPALS (PLS1, PLS2)": {
                "parameters": ["Components: [1, 2, 3, 5, 8, 10]", "Mode: [PLS1, PLS2]", "Max Iterations: [500, 1000]", "Tolerance: [1e-6, 1e-5, 1e-4]"],
                "total": 48
            },
            "SIMPLS": {
                "parameters": ["Components: [1, 2, 3, 5, 8, 10, 15]"],
                "total": 7
            },
            "Multilayer Perceptron (MLP)": {
                "parameters": ["Hidden Layers: [(50,), (100,), (50,50), (100,50)]", "Activation: [relu, tanh]", "Alpha: [0.0001, 0.001, 0.01]", "Solver: [adam, lbfgs]"],
                "total": 24
            },

            "ε-Support Vector Regression (ε-SVR)": {
                "parameters": ["C: [0.1, 1.0, 10.0, 100.0]", "Epsilon: [0.01, 0.1, 0.2]", "Kernel: [rbf, linear, poly]", "Gamma: [scale, auto]"],
                "total": 24
            },
            "Nu-SVR": {
                "parameters": ["C: [0.1, 1.0, 10.0]", "Nu: [0.1, 0.3, 0.5, 0.7]", "Kernel: [rbf, linear]"],
                "total": 24
            },
            "XGBoost": {
                "parameters": ["N_estimators: [50, 100, 200]", "Max_depth: [3, 6, 9]", "Learning_rate: [0.01, 0.1, 0.2]", "Subsample: [0.8, 1.0]"],
                "total": 54
            }
        }

        if model in grid_info:
            info = grid_info[model]
            with st.expander(f"📊 {model} - Grid Search Details"):
                st.markdown("**Parameter Ranges to Test:**")
                for param in info["parameters"]:
                    st.write(f"• {param}")
                st.write(f"• **Total Combinations**: {info['total']} configurations")

    def _run_model_grid_search(self, X: np.ndarray, y: np.ndarray, model_name: str, cv_folds: int, optimization_metric: str, n_jobs: int) -> Dict[str, Any]:
        """Run grid search for a specific model."""
        # This is a placeholder - would need to implement full grid search logic
        # For now, return a simple result
        kf = KFold(n_splits=cv_folds, shuffle=True, random_state=42)

        # Simple example with PLS
        if model_name in ["NIPALS (PLS1, PLS2)", "SIMPLS"]:
            best_rmsecv = float('inf')
            best_r2 = -1
            best_params = {}
            best_model = None
            grid_results = []

            for n_comp in range(1, min(11, X.shape[1])):
                model = PLSRegression(n_components=n_comp)
                scores = cross_val_score(model, X, y, cv=kf, scoring='neg_mean_squared_error')
                rmsecv = np.sqrt(-scores.mean())
                r2_scores = cross_val_score(model, X, y, cv=kf, scoring='r2')
                r2 = r2_scores.mean()

                grid_results.append({
                    'n_components': n_comp,
                    'rmsecv': rmsecv,
                    'r2': r2
                })

                if rmsecv < best_rmsecv:
                    best_rmsecv = rmsecv
                    best_r2 = r2
                    best_params = {'n_components': n_comp}
                    best_model = model

            # Train best model
            best_model.fit(X, y)

            return {
                'best_model': best_model,
                'best_rmsecv': best_rmsecv,
                'best_r2': best_r2,
                'best_params': best_params,
                'grid_results': grid_results,
                'model_name': model_name,
                'optimization_metric': optimization_metric
            }

        # Placeholder for other models
        return {
            'best_model': None,
            'best_rmsecv': 0.1,
            'best_r2': 0.9,
            'best_params': {},
            'grid_results': [],
            'model_name': model_name,
            'optimization_metric': optimization_metric
        }

    def _render_stage2_results_evaluation(self) -> None:
        """Render Stage 2: Results & Evaluation."""
        st.markdown("### 📊 Stage 2: Results & Evaluation")

        if not self.session.has("model_selection_results"):
            st.error("❌ No model selection results found. Please complete Stage 1 first.")
            return

        results = self.session.get("model_selection_results")
        route_type = results["route_type"]
        selected_model = results["selected_model"]

        # Results summary
        st.markdown("#### 🏆 Model Selection Results Summary")

        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Route Type", "Manual" if route_type == "manual" else "Automatic")
        with col2:
            st.metric("Selected Model", selected_model)
        with col3:
            if route_type == "manual":
                st.metric("RMSECV", f"{results['results']['rmsecv']:.4f}")
            else:
                st.metric("Best RMSECV", f"{results['results']['best_rmsecv']:.4f}")
        with col4:
            if route_type == "manual":
                st.metric("R²", f"{results['results']['r2']:.4f}")
            else:
                st.metric("Best R²", f"{results['results']['best_r2']:.4f}")

        # Route-specific results display
        if route_type == "automatic":
            self._render_grid_search_results_display(results)
        else:
            self._render_manual_results_display(results)

        # Model visualization
        self._render_model_performance_plots(results)

        # Stage 2 navigation
        st.markdown("---")
        st.markdown("#### 🎯 Proceed to Model Application")

        if st.button("🎯 Apply Model Configuration", type="primary", key="proceed_to_stage3"):
            self.session.set("model_selection_stage", 3)
            st.rerun()

    def _render_stage3_model_application(self) -> None:
        """Render Stage 3: Model Application."""
        st.markdown("### 🎯 Stage 3: Model Application")

        if not self.session.has("model_selection_results"):
            st.error("❌ No model selection results found. Please complete previous stages first.")
            return

        results = self.session.get("model_selection_results")

        # Show current model summary
        st.markdown("#### 📋 Current Model Summary")

        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Route Type", "Manual" if results["route_type"] == "manual" else "Automatic")
        with col2:
            st.metric("Selected Model", results["selected_model"])
        with col3:
            if results["route_type"] == "manual":
                st.metric("RMSECV", f"{results['results']['rmsecv']:.4f}")
            else:
                st.metric("Best RMSECV", f"{results['results']['best_rmsecv']:.4f}")
        with col4:
            if results["route_type"] == "manual":
                st.metric("R²", f"{results['results']['r2']:.4f}")
            else:
                st.metric("Best R²", f"{results['results']['best_r2']:.4f}")

        # Apply model button
        st.markdown("#### ✅ Apply Model Configuration")

        col1, col2 = st.columns([3, 1])
        with col1:
            st.write("Apply the selected model configuration for prediction on unknown samples.")

        with col2:
            if st.button("✅ Apply Model", type="primary", key="apply_model_configuration"):
                try:
                    # Store final model configuration
                    self.session.set("final_model_config", results)
                    self.session.set("model_selection_applied", True)

                    st.success("✅ Model configuration applied successfully!")
                    st.info("🚀 You can now proceed to prediction/validation steps.")
                    st.rerun()

                except Exception as e:
                    st.error(f"Error applying model configuration: {str(e)}")

        # Show application status
        if self.session.has("model_selection_applied") and self.session.get("model_selection_applied"):
            st.success("✅ Model configuration has been applied! Ready for prediction.")
        else:
            st.warning("⚠️ Model configuration not yet applied. Click the button above to apply.")

    def _render_grid_search_results_display(self, results: Dict[str, Any]) -> None:
        """Render grid search results display with ChatGPT help."""
        # Grid Search header with help icon
        col1, col2 = st.columns([4, 1])
        with col1:
            st.markdown("#### 📊 Grid Search Optimization Results")
        with col2:
            # Create contextual prompt for ChatGPT help
            selected_model = results["algorithm"]
            optimization_metric = results.get("optimization_metric", "RMSECV")
            best_rmsecv = results["results"]["best_rmsecv"]
            best_r2 = results["results"]["best_r2"]
            best_params = results["results"]["best_params"]

            # Build best parameters string
            best_params_str = ", ".join([f"{param}: {value}" for param, value in best_params.items()])

            contextual_prompt = f"""Please explain the model selection grid search results for chemometrics:

**Model Used**: {selected_model}
**Optimization Results**:
- Best RMSECV achieved: {best_rmsecv:.4f}
- Best R² achieved: {best_r2:.4f}
- Optimization metric: {optimization_metric}

**Best Parameters Found**: {best_params_str}

Please help me understand:
1. What does the RMSECV value of {best_rmsecv:.4f} indicate about the model performance?
2. How should I interpret the R² value of {best_r2:.4f}?
3. How should I interpret the best parameters found during optimization?
4. Is this model performance suitable for chemometric analysis?
5. What are the next steps I should take in my chemometric workflow?
6. Are there any potential concerns or recommendations based on these results?

Please provide practical guidance for interpreting these model selection results and recommendations for proceeding with the analysis."""

            ChatGPTHelper.create_help_icon(
                "Model Selection Grid Search Results",
                "interpreting model selection optimization results in chemometrics",
                contextual_prompt
            )

        # Best parameters display
        if results["results"]["best_params"]:
            st.markdown("**🔧 Best Parameters:**")
            for param, value in results["results"]["best_params"].items():
                st.write(f"• **{param}**: {value}")

        # Grid search results table
        if results["results"]["grid_results"]:
            with st.expander("📋 All Grid Search Results"):
                grid_df = pd.DataFrame(results["results"]["grid_results"])
                grid_df = grid_df.sort_values('rmsecv')
                grid_df['rmsecv'] = grid_df['rmsecv'].round(4)
                if 'r2' in grid_df.columns:
                    grid_df['r2'] = grid_df['r2'].round(4)
                st.dataframe(grid_df, use_container_width=True)

                # Download grid results
                csv = grid_df.to_csv(index=False)
                st.download_button(
                    label="📥 Download Grid Search Results",
                    data=csv,
                    file_name=f"{selected_model.replace(' ', '_')}_grid_search_results.csv",
                    mime="text/csv"
                )

    def _render_manual_results_display(self, results: Dict[str, Any]) -> None:
        """Render manual configuration results display."""
        st.markdown("#### ⚙️ Manual Configuration Results")

        # Model parameters display
        if results.get("model_params"):
            st.markdown("**🔧 Model Parameters:**")
            for param, value in results["model_params"].items():
                st.write(f"• **{param}**: {value}")

        # Cross-validation details
        st.markdown("**🔄 Cross-Validation Details:**")
        st.write(f"• **CV Folds**: {results['cv_folds']}")
        st.write(f"• **RMSECV**: {results['results']['rmsecv']:.4f}")
        st.write(f"• **R²**: {results['results']['r2']:.4f}")

    def _render_model_performance_plots(self, results: Dict[str, Any]) -> None:
        """Render comprehensive model performance visualization plots."""
        optimization_route = results["optimization_route"]
        algorithm = results["algorithm"]

        # Get training data first
        x_train = self.session.get("x_train_selected")
        y_train = self.session.get("y_train")

        if x_train is None or y_train is None:
            st.warning("⚠️ Training data not available for visualization.")
            return

        # Convert to numpy arrays
        x_array = x_train.select_dtypes(include=[np.number]).values
        y_array = y_train.select_dtypes(include=[np.number]).values
        if y_array.ndim == 2 and y_array.shape[1] == 1:
            y_array = y_array.ravel()

        # Get model and performance data based on optimization route
        if optimization_route == "automatic":
            # For automatic grid search, get the results from training
            training_results = results.get("results", {})
            best_model = training_results.get("best_model")

            if best_model is not None:
                # Generate predictions using the best model
                y_pred = best_model.predict(x_array)

                # Use the stored grid search results for cross-validation data
                model_results = {
                    "best_rmsecv": training_results.get("best_rmsecv", 0),
                    "best_r2": training_results.get("best_r2", 0),
                    "cv_scores": [],  # Will be calculated if needed
                    "r2_scores": [],  # Will be calculated if needed
                    "best_params": training_results.get("best_params", {})
                }

                # Create performance plots with correct data
                self._create_performance_plots(y_array, y_pred, algorithm, model_results)
            else:
                # Try to get from comprehensive grid search results
                best_config = self.session.get("best_model_config")
                if best_config and "model" in best_config:
                    best_model = best_config["model"]
                    y_pred = best_model.predict(x_array)

                    model_results = {
                        "best_rmsecv": best_config.get("rmsecv", 0),
                        "best_r2": best_config.get("r2", 0),
                        "cv_scores": best_config.get("cv_scores", []),
                        "r2_scores": best_config.get("r2_scores", []),
                        "best_params": best_config.get("params", {})
                    }

                    self._create_performance_plots(y_array, y_pred, algorithm, model_results)
                else:
                    st.warning("⚠️ Model results are available but visualization data is incomplete. This may be due to a recent grid search completion.")

        else:
            # Manual configuration
            model_results = results["results"]
            trained_model = model_results.get("model")

            if trained_model is not None:
                # Generate predictions
                y_pred = trained_model.predict(x_array)

                # Create performance plots
                self._create_performance_plots(y_array, y_pred, algorithm, model_results)
            else:
                st.warning("⚠️ Trained model not available for visualization.")

    def _create_performance_plots(self, y_true: np.ndarray, y_pred: np.ndarray, algorithm: str, model_results: Dict[str, Any]) -> None:
        """Create comprehensive performance visualization plots."""
        import plotly.graph_objects as go
        from plotly.subplots import make_subplots

        # Create subplots
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=(
                'Measured vs Predicted',
                'Residuals Plot',
                'Prediction Error Distribution',
                'Cross-Validation Scores'
            ),
            specs=[[{"secondary_y": False}, {"secondary_y": False}],
                   [{"secondary_y": False}, {"secondary_y": False}]]
        )

        # 1. Measured vs Predicted plot
        # Calculate R² for the plot
        ss_res = np.sum((y_true - y_pred) ** 2)
        ss_tot = np.sum((y_true - np.mean(y_true)) ** 2)
        r2_calc = 1 - (ss_res / ss_tot)

        fig.add_trace(
            go.Scatter(
                x=y_true,
                y=y_pred,
                mode='markers',
                name='Predictions',
                marker=dict(color='blue', size=8, opacity=0.7),
                text=[f'Sample {i+1}' for i in range(len(y_true))],
                hovertemplate='Measured: %{x:.3f}<br>Predicted: %{y:.3f}<br>%{text}<extra></extra>'
            ),
            row=1, col=1
        )

        # Add perfect prediction line
        min_val = min(y_true.min(), y_pred.min())
        max_val = max(y_true.max(), y_pred.max())
        fig.add_trace(
            go.Scatter(
                x=[min_val, max_val],
                y=[min_val, max_val],
                mode='lines',
                name='Perfect Prediction',
                line=dict(color='red', dash='dash'),
                showlegend=False
            ),
            row=1, col=1
        )

        # 2. Residuals plot
        residuals = y_true - y_pred
        fig.add_trace(
            go.Scatter(
                x=y_pred,
                y=residuals,
                mode='markers',
                name='Residuals',
                marker=dict(color='green', size=8, opacity=0.7),
                hovertemplate='Predicted: %{x:.3f}<br>Residual: %{y:.3f}<extra></extra>'
            ),
            row=1, col=2
        )

        # Add zero line for residuals
        fig.add_hline(y=0, line_dash="dash", line_color="red", row=1, col=2)

        # 3. Prediction error distribution
        fig.add_trace(
            go.Histogram(
                x=residuals,
                name='Error Distribution',
                nbinsx=20,
                marker_color='orange',
                opacity=0.7
            ),
            row=2, col=1
        )

        # 4. Cross-validation scores (if available)
        cv_scores = model_results.get("cv_scores", [])
        r2_scores = model_results.get("r2_scores", [])

        if len(cv_scores) > 0:
            # Convert negative MSE to RMSE
            rmse_scores = np.sqrt(-np.array(cv_scores))
            cv_folds = list(range(1, len(rmse_scores) + 1))

            fig.add_trace(
                go.Scatter(
                    x=cv_folds,
                    y=rmse_scores,
                    mode='lines+markers',
                    name='CV RMSE',
                    line=dict(color='purple', width=2),
                    marker=dict(size=8)
                ),
                row=2, col=2
            )
        else:
            # Show a placeholder message
            fig.add_annotation(
                text="CV scores not available",
                x=0.5, y=0.5,
                xref="x4", yref="y4",
                showarrow=False,
                font=dict(size=14, color="gray")
            )

        # Update layout
        fig.update_layout(
            title=f"{algorithm} - Model Performance Analysis",
            height=800,
            showlegend=True,
            template="plotly_white"
        )

        # Update axes labels
        fig.update_xaxes(title_text="Measured Values", row=1, col=1)
        fig.update_yaxes(title_text="Predicted Values", row=1, col=1)
        fig.update_xaxes(title_text="Predicted Values", row=1, col=2)
        fig.update_yaxes(title_text="Residuals", row=1, col=2)
        fig.update_xaxes(title_text="Residuals", row=2, col=1)
        fig.update_yaxes(title_text="Frequency", row=2, col=1)
        fig.update_xaxes(title_text="CV Fold", row=2, col=2)
        fig.update_yaxes(title_text="RMSE", row=2, col=2)

        st.plotly_chart(fig, use_container_width=True)

        # Performance metrics summary
        st.markdown("##### 📊 Performance Metrics Summary")

        # Important note about training vs CV performance
        st.info("""
        **📝 Important Note:** Training performance shows how well the model fits the training data,
        while cross-validation performance indicates how well the model generalizes to unseen data.
        CV performance is more reliable for assessing model quality.
        """)

        # Calculate training set metrics
        rmse_train = np.sqrt(np.mean((y_true - y_pred) ** 2))
        mae_train = np.mean(np.abs(y_true - y_pred))
        mape_train = np.mean(np.abs((y_true - y_pred) / y_true)) * 100

        # Display both training and cross-validation metrics
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("**Training Set Performance:**")
            subcol1, subcol2 = st.columns(2)
            with subcol1:
                st.metric("Training R²", f"{r2_calc:.4f}")
                st.metric("Training MAE", f"{mae_train:.4f}")
            with subcol2:
                st.metric("Training RMSE", f"{rmse_train:.4f}")
                st.metric("Training MAPE", f"{mape_train:.2f}%")

        with col2:
            st.markdown("**Cross-Validation Performance:**")
            # Get CV metrics from model results
            cv_rmsecv = model_results.get("best_rmsecv", model_results.get("rmsecv", "N/A"))
            cv_r2 = model_results.get("best_r2", model_results.get("r2", "N/A"))

            subcol1, subcol2 = st.columns(2)
            with subcol1:
                if cv_r2 != "N/A":
                    st.metric("CV R²", f"{cv_r2:.4f}")
                else:
                    st.metric("CV R²", "N/A")
                st.metric("CV Method", "K-Fold")
            with subcol2:
                if cv_rmsecv != "N/A":
                    st.metric("CV RMSECV", f"{cv_rmsecv:.4f}")
                else:
                    st.metric("CV RMSECV", "N/A")
                # Show number of CV folds if available
                cv_scores = model_results.get("cv_scores", [])
                if len(cv_scores) > 0:
                    st.metric("CV Folds", len(cv_scores))
                else:
                    st.metric("CV Folds", "N/A")

        # Model diagnostics
        st.markdown("##### 🔍 Model Diagnostics")

        col1, col2 = st.columns(2)
        with col1:
            st.markdown("**Residual Analysis:**")
            residual_mean = np.mean(residuals)
            residual_std = np.std(residuals)
            st.write(f"• **Mean Residual**: {residual_mean:.6f}")
            st.write(f"• **Residual Std**: {residual_std:.4f}")

            # Check for bias
            if abs(residual_mean) < 0.001:
                st.success("✅ Low bias (mean residual ≈ 0)")
            else:
                st.warning(f"⚠️ Potential bias detected (mean residual = {residual_mean:.6f})")

        with col2:
            st.markdown("**Prediction Quality:**")
            # Calculate percentage of predictions within certain error bounds
            error_bounds = [0.05, 0.10, 0.15]
            relative_errors = np.abs(residuals / y_true)

            for bound in error_bounds:
                within_bound = np.sum(relative_errors <= bound) / len(relative_errors) * 100
                st.write(f"• **Within {bound*100:.0f}% error**: {within_bound:.1f}% of predictions")

        # Additional cross-validation details (if available)
        cv_scores = model_results.get("cv_scores", [])
        r2_scores = model_results.get("r2_scores", [])

        if len(cv_scores) > 0:
            st.markdown("##### 🔄 Cross-Validation Details")
            rmse_cv = np.sqrt(-np.mean(cv_scores))
            rmse_cv_std = np.sqrt(np.var(-cv_scores))

            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("CV RMSE Mean", f"{rmse_cv:.4f}")
            with col2:
                st.metric("CV RMSE Std", f"{rmse_cv_std:.4f}")
            with col3:
                if len(r2_scores) > 0:
                    r2_cv_mean = np.mean(r2_scores)
                    r2_cv_std = np.std(r2_scores)
                    st.metric("CV R² Mean", f"{r2_cv_mean:.4f}")
                    st.caption(f"CV R² Std: {r2_cv_std:.4f}")
                else:
                    st.metric("CV R² Mean", "N/A")

            # Show consistency check
            st.markdown("**🔍 Consistency Check:**")
            if abs(rmse_cv - cv_rmsecv) < 0.001 if cv_rmsecv != "N/A" else False:
                st.success("✅ CV metrics are consistent")
            else:
                st.info("ℹ️ CV metrics calculated from individual fold scores")

    def _render_automatic_model_selection(self, x_train: pd.DataFrame, y_train: pd.DataFrame) -> None:
        """Render automatic model selection with comprehensive grid search."""
        col1, col2 = st.columns([4, 1])
        with col1:
            st.markdown("### 🤖 Automatic Model Selection")
        with col2:
            ChatGPTHelper.create_help_icon(
                "Automatic Model Selection",
                "understanding automatic model selection",
                "Please explain automatic model selection in chemometrics, including different algorithms and grid search optimization."
            )

        st.info("🔍 Comprehensive grid search: Tests multiple algorithms with optimized parameters using selected variables")

        # Algorithm selection for grid search
        st.markdown("#### 📋 Select Algorithms to Test")

        col1, col2 = st.columns(2)

        with col1:
            st.markdown("**PLS Algorithms:**")
            pls_algorithms = []
            if st.checkbox("NIPALS (PLS1/PLS2)", value=True, help="Most common in chemometrics; handles missing data well"):
                pls_algorithms.append("NIPALS")
            if st.checkbox("SIMPLS", value=True, help="Computationally efficient; good orthogonality"):
                pls_algorithms.append("SIMPLS")
            if st.checkbox("Standard PLS", value=False, help="Basic scikit-learn PLS implementation"):
                pls_algorithms.append("Standard PLS")

        with col2:
            st.markdown("**Machine Learning Algorithms:**")
            ml_algorithms = []
            if st.checkbox("Multilayer Perceptron (MLP)", value=True, help="Most common ANN structure for regression"):
                ml_algorithms.append("MLP")
            if st.checkbox("Backpropagation Neural Network", value=False, help="Basic MLP trained using backpropagation"):
                ml_algorithms.append("BPNN")
            if st.checkbox("ε-Support Vector Regression", value=True, help="Most standard SVR for regression tasks"):
                ml_algorithms.append("SVR")
            if st.checkbox("Nu-SVR", value=False, help="Alternative to ε-SVR using ν parameter"):
                ml_algorithms.append("Nu-SVR")

            # XGBoost with availability check
            if HAS_XGBOOST:
                if st.checkbox("XGBoost", value=True, help="Default regression booster"):
                    ml_algorithms.append("XGBoost")
            else:
                st.checkbox("XGBoost", value=False, disabled=True, help="XGBoost not available. Install with: pip install xgboost")
                if st.button("Install XGBoost", key="install_xgboost_step5", help="Click to see installation instructions"):
                    st.info("To install XGBoost, run: `pip install xgboost` in your terminal")

        # Grid search parameters
        st.markdown("#### ⚙️ Grid Search Parameters")

        col1, col2, col3 = st.columns(3)

        with col1:
            max_components = st.slider(
                "Max PLS Components",
                min_value=1,
                max_value=min(15, x_train.shape[1], x_train.shape[0]-2),
                value=min(self.session.get("recommended_components", 8), x_train.shape[1]//2),
                help="Maximum number of PLS components to test"
            )

        with col2:
            cv_folds = st.slider(
                "Cross-Validation Folds",
                min_value=3,
                max_value=10,
                value=5,
                help="Number of CV folds for evaluation"
            )

        with col3:
            n_jobs = st.slider(
                "Parallel Jobs",
                min_value=1,
                max_value=4,
                value=2,
                help="Number of parallel jobs for grid search"
            )

        # Run grid search
        if st.button("🚀 Run Comprehensive Grid Search", type="primary", key="run_grid_search_step5"):
            all_algorithms = pls_algorithms + ml_algorithms

            if not all_algorithms:
                st.warning("Please select at least one algorithm to test.")
                return

            with st.spinner(f"Running grid search on {len(all_algorithms)} algorithms with selected variables... This may take several minutes."):
                try:
                    # Convert to numpy arrays
                    x_array = x_train.select_dtypes(include=[np.number]).values
                    y_array = y_train.select_dtypes(include=[np.number]).values

                    if y_array.ndim == 2 and y_array.shape[1] == 1:
                        y_array = y_array.ravel()

                    # Run comprehensive grid search
                    results_df, best_config = self._run_comprehensive_grid_search(
                        x_array, y_array, all_algorithms, max_components, cv_folds, n_jobs
                    )

                    # Store results with proper model information
                    self.session.set("grid_search_results", results_df)
                    self.session.set("best_model_config", best_config)
                    self.session.set("model_selection_complete", True)

                    # Also store in the training results format for visualization
                    self.session.set("model_training_results", {
                        "optimization_route": "automatic",
                        "algorithm": best_config['algorithm'],
                        "results": {
                            "best_model": best_config.get('model'),
                            "best_rmsecv": best_config['rmsecv'],
                            "best_r2": best_config['r2'],
                            "best_params": best_config.get('params', {}),
                            "grid_results": results_df.to_dict('records')
                        }
                    })

                    # Display results
                    st.success(f"✅ Grid search complete! Tested {len(results_df)} configurations.")
                    st.info(f"🏆 Best model: **{best_config['algorithm']}** (RMSECV: {best_config['rmsecv']:.4f}, R²: {best_config['r2']:.4f})")

                except Exception as e:
                    st.error(f"Error during grid search: {str(e)}")

        # Show results if available
        if self.session.has("grid_search_results"):
            self._render_grid_search_results("automatic")





    def _run_comprehensive_grid_search(self, X: np.ndarray, y: np.ndarray, algorithms: List[str],
                                     max_components: int, cv_folds: int, n_jobs: int) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """Run comprehensive grid search across multiple algorithms."""
        results = []

        # Flatten y if it's 2D with single column
        if y.ndim == 2 and y.shape[1] == 1:
            y = y.ravel()

        kf = KFold(n_splits=cv_folds, shuffle=True, random_state=42)

        for algorithm in algorithms:
            try:
                if algorithm == "NIPALS":
                    # NIPALS algorithm
                    for n_comp in range(1, max_components + 1):
                        model = NIPALSRegression(n_components=n_comp, mode='PLS1')
                        scores = cross_val_score(model, X, y, cv=kf, scoring='neg_mean_squared_error', n_jobs=1)
                        rmsecv = np.sqrt(-scores.mean())
                        r2_scores = cross_val_score(model, X, y, cv=kf, scoring='r2', n_jobs=1)
                        r2 = r2_scores.mean()

                        results.append({
                            'Algorithm': 'NIPALS',
                            'Parameters': f'n_components={n_comp}',
                            'RMSECV': rmsecv,
                            'R²': r2,
                            'Config': {'algorithm': 'NIPALS', 'n_components': n_comp}
                        })
                elif algorithm == "SIMPLS":
                    # SIMPLS algorithm (scikit-learn)
                    for n_comp in range(1, max_components + 1):
                        model = PLSRegression(n_components=n_comp)
                        scores = cross_val_score(model, X, y, cv=kf, scoring='neg_mean_squared_error', n_jobs=1)
                        rmsecv = np.sqrt(-scores.mean())
                        r2_scores = cross_val_score(model, X, y, cv=kf, scoring='r2', n_jobs=1)
                        r2 = r2_scores.mean()

                        results.append({
                            'Algorithm': algorithm,
                            'Parameters': f'n_components={n_comp}',
                            'RMSECV': rmsecv,
                            'R²': r2,
                            'Config': {'algorithm': algorithm, 'n_components': n_comp}
                        })

                elif algorithm == "Standard PLS":
                    # Standard PLS
                    for n_comp in range(1, max_components + 1):
                        model = PLSRegression(n_components=n_comp)
                        scores = cross_val_score(model, X, y, cv=kf, scoring='neg_mean_squared_error', n_jobs=1)
                        rmsecv = np.sqrt(-scores.mean())
                        r2_scores = cross_val_score(model, X, y, cv=kf, scoring='r2', n_jobs=1)
                        r2 = r2_scores.mean()

                        results.append({
                            'Algorithm': algorithm,
                            'Parameters': f'n_components={n_comp}',
                            'RMSECV': rmsecv,
                            'R²': r2,
                            'Config': {'algorithm': algorithm, 'n_components': n_comp}
                        })

                elif algorithm == "MLP":
                    # Multilayer Perceptron
                    hidden_sizes = [(50,), (100,), (50, 50)]
                    activations = ['relu', 'tanh']
                    alphas = [0.0001, 0.001]

                    for hidden in hidden_sizes:
                        for activation in activations:
                            for alpha in alphas:
                                try:
                                    model = MLPRegressor(
                                        hidden_layer_sizes=hidden,
                                        activation=activation,
                                        alpha=alpha,
                                        max_iter=200,
                                        random_state=42
                                    )
                                    scores = cross_val_score(model, X, y, cv=kf, scoring='neg_mean_squared_error', n_jobs=1)
                                    rmsecv = np.sqrt(-scores.mean())
                                    r2_scores = cross_val_score(model, X, y, cv=kf, scoring='r2', n_jobs=1)
                                    r2 = r2_scores.mean()

                                    results.append({
                                        'Algorithm': algorithm,
                                        'Parameters': f'hidden={hidden}, activation={activation}, alpha={alpha}',
                                        'RMSECV': rmsecv,
                                        'R²': r2,
                                        'Config': {'algorithm': algorithm, 'hidden_layer_sizes': hidden, 'activation': activation, 'alpha': alpha}
                                    })
                                except:
                                    continue

                elif algorithm in ["SVR", "Nu-SVR"]:
                    # Support Vector Regression
                    C_values = [0.1, 1.0, 10.0]
                    kernels = ['rbf', 'linear']

                    for C in C_values:
                        for kernel in kernels:
                            try:
                                if algorithm == "SVR":
                                    model = SVR(C=C, kernel=kernel, epsilon=0.1)
                                else:
                                    model = NuSVR(C=C, kernel=kernel, nu=0.5)

                                scores = cross_val_score(model, X, y, cv=kf, scoring='neg_mean_squared_error', n_jobs=1)
                                rmsecv = np.sqrt(-scores.mean())
                                r2_scores = cross_val_score(model, X, y, cv=kf, scoring='r2', n_jobs=1)
                                r2 = r2_scores.mean()

                                results.append({
                                    'Algorithm': algorithm,
                                    'Parameters': f'C={C}, kernel={kernel}',
                                    'RMSECV': rmsecv,
                                    'R²': r2,
                                    'Config': {'algorithm': algorithm, 'C': C, 'kernel': kernel}
                                })
                            except:
                                continue

                elif algorithm == "XGBoost" and HAS_XGBOOST:
                    # XGBoost
                    n_estimators_list = [50, 100, 200]
                    max_depths = [3, 6, 9]
                    learning_rates = [0.1, 0.2]

                    for n_est in n_estimators_list:
                        for max_depth in max_depths:
                            for lr in learning_rates:
                                try:
                                    rmsecv, r2 = self._test_xgboost_configuration(
                                        X, y, kf,
                                        n_estimators=n_est,
                                        max_depth=max_depth,
                                        learning_rate=lr
                                    )

                                    results.append({
                                        'Algorithm': algorithm,
                                        'Parameters': f'n_est={n_est}, depth={max_depth}, lr={lr}',
                                        'RMSECV': rmsecv,
                                        'R²': r2,
                                        'Config': {'algorithm': algorithm, 'n_estimators': n_est, 'max_depth': max_depth, 'learning_rate': lr}
                                    })
                                except Exception as e:
                                    st.warning(f"Error testing XGBoost configuration: {str(e)}")
                                    continue

            except Exception as e:
                st.warning(f"Error testing {algorithm}: {str(e)}")
                continue

        # Convert to DataFrame and sort
        results_df = pd.DataFrame(results)
        if not results_df.empty:
            results_df = results_df.sort_values(['RMSECV', 'R²'], ascending=[True, False])

            # Get best configuration and train the best model
            best_row = results_df.iloc[0]
            best_config = best_row['Config'].copy()
            best_config['rmsecv'] = best_row['RMSECV']
            best_config['r2'] = best_row['R²']
            best_config['parameters'] = best_row['Parameters']

            # Train the best model with the best configuration
            best_algorithm = best_config['algorithm']
            best_model = None

            try:
                if best_algorithm == "Enhanced ANN (MATLAB-style)":
                    best_model = EnhancedANN(
                        hidden_layer_sizes=best_config.get('hidden_layer_sizes', (10,)),
                        activation=best_config.get('activation', 'tanh'),
                        solver=best_config.get('solver', 'lbfgs'),
                        data_division=best_config.get('data_division', 'sequential'),
                        max_iter=300,
                        random_state=42
                    )
                elif best_algorithm == "Multilayer Perceptron (MLP)":
                    best_model = MLPRegressor(
                        hidden_layer_sizes=best_config.get('hidden_layer_sizes', (10,)),
                        activation=best_config.get('activation', 'relu'),
                        solver=best_config.get('solver', 'adam'),
                        alpha=best_config.get('alpha', 0.0001),
                        max_iter=300,
                        random_state=42
                    )

                elif "NIPALS" in best_algorithm:
                    best_model = NIPALSRegression(n_components=best_config.get('n_components', min(10, X.shape[1]//2)))
                elif "SIMPLS" in best_algorithm:
                    best_model = PLSRegression(n_components=best_config.get('n_components', min(10, X.shape[1]//2)))
                elif "ε-SVR" in best_algorithm:
                    best_model = SVR(
                        C=best_config.get('C', 1.0),
                        epsilon=best_config.get('epsilon', 0.1),
                        kernel=best_config.get('kernel', 'rbf')
                    )
                elif "Nu-SVR" in best_algorithm:
                    best_model = NuSVR(
                        C=best_config.get('C', 1.0),
                        nu=best_config.get('nu', 0.5),
                        kernel=best_config.get('kernel', 'rbf')
                    )
                elif "XGBoost" in best_algorithm and HAS_XGBOOST:
                    import xgboost as xgb
                    best_model = xgb.XGBRegressor(
                        n_estimators=best_config.get('n_estimators', 100),
                        max_depth=best_config.get('max_depth', 6),
                        learning_rate=best_config.get('learning_rate', 0.1),
                        random_state=42
                    )
                else:
                    # Fallback to PLS
                    best_model = PLSRegression(n_components=min(5, X.shape[1]//2))

                # Train the best model
                if best_model is not None:
                    best_model.fit(X, y)
                    best_config['model'] = best_model

            except Exception as e:
                st.warning(f"Could not train best model: {str(e)}")
                # Create a fallback model
                best_model = PLSRegression(n_components=min(5, X.shape[1]//2))
                best_model.fit(X, y)
                best_config['model'] = best_model
        else:
            best_config = {'algorithm': 'Standard PLS', 'n_components': 1, 'rmsecv': float('inf'), 'r2': -1.0}

        return results_df, best_config

    def _create_xgboost_model(self, **params):
        """Create XGBoost model with proper error handling."""
        if not HAS_XGBOOST:
            raise ImportError("XGBoost not available")

        try:
            import xgboost as xgb
            return xgb.XGBRegressor(
                objective='reg:squarederror',
                random_state=42,
                **params
            )
        except Exception as e:
            raise ImportError(f"Error creating XGBoost model: {str(e)}")

    def _test_xgboost_configuration(self, X, y, cv, **params):
        """Test a specific XGBoost configuration."""
        try:
            model = self._create_xgboost_model(**params)
            scores = cross_val_score(model, X, y, cv=cv, scoring='neg_mean_squared_error', n_jobs=1)
            rmsecv = np.sqrt(-scores.mean())
            r2_scores = cross_val_score(model, X, y, cv=cv, scoring='r2', n_jobs=1)
            r2 = r2_scores.mean()
            return rmsecv, r2
        except Exception as e:
            return float('inf'), -1.0

    def _render_grid_search_results(self, context: str = "default") -> None:
        """Render grid search results."""
        results_df = self.session.get("grid_search_results")
        best_config = self.session.get("best_model_config")

        if results_df is None or best_config is None:
            return

        st.markdown("#### 📊 Grid Search Results")

        # Best configuration summary
        st.info(f"🏆 Best model: **{best_config['algorithm']}** (RMSECV: {best_config['rmsecv']:.4f}, R²: {best_config['r2']:.4f})")
        st.markdown(f"**Parameters:** {best_config.get('parameters', 'N/A')}")

        # Top 10 results
        st.markdown("**Top 10 Best Configurations:**")
        display_df = results_df.head(10).copy()
        display_df['RMSECV'] = display_df['RMSECV'].round(4)
        display_df['R²'] = display_df['R²'].round(4)
        st.dataframe(display_df[['Algorithm', 'Parameters', 'RMSECV', 'R²']], use_container_width=True)

        # Summary statistics
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Total Configurations", len(results_df))
        with col2:
            st.metric("Best RMSECV", f"{results_df.iloc[0]['RMSECV']:.4f}")
        with col3:
            st.metric("Best R²", f"{results_df.iloc[0]['R²']:.4f}")

        # Apply best configuration with unique key based on context
        button_key = f"apply_best_grid_config_step5_{context}"
        if st.button("Apply Best Configuration", key=button_key, type="primary"):
            self.session.set("selected_model_config", best_config)
            st.success(f"✅ Applied best configuration: {best_config['algorithm']}")
            st.rerun()

    def _render_model_evaluation(self) -> None:
        """Render model evaluation results."""
        st.markdown("### 📊 Model Evaluation")

        if self.session.has("grid_search_results"):
            st.markdown("#### 🏆 Grid Search Results")
            self._render_grid_search_results("evaluation")
        elif self.session.has("manual_model_config"):
            st.markdown("#### ⚙️ Manual Configuration")
            config = self.session.get("manual_model_config")

            col1, col2 = st.columns(2)
            with col1:
                st.metric("Algorithm", config["algorithm"])
            with col2:
                st.metric("Category", config["category"])

            # Display parameters
            st.markdown("**Parameters:**")
            for key, value in config["parameters"].items():
                st.write(f"- **{key}**: {value}")
        else:
            st.info("No model configuration available. Please complete automatic grid search or manual configuration first.")

            # Show sample data option
            if st.button("Load Sample Configuration", type="secondary", key="load_sample_step5"):
                sample_config = {
                    "algorithm": "NIPALS (PLS1/PLS2)",
                    "category": "PLS Algorithms",
                    "parameters": {"n_components": 5, "mode": "PLS2", "max_iter": 500, "tol": 1e-6}
                }
                self.session.set("manual_model_config", sample_config)
                self.session.set("model_selection_complete", True)
                st.success("✅ Sample configuration loaded!")
                st.rerun()

    def _render_navigation_section(self) -> None:
        """Render navigation buttons."""
        st.markdown("---")

        # Check if model selection is complete
        model_complete = self.session.has("model_selection_complete")

        clicked = self.render_navigation_buttons(
            show_previous=True,
            next_enabled=model_complete,
            custom_next_text="Next: Final Model →" if model_complete else "Complete Model Selection First"
        )

        if not model_complete and clicked.get("next", False):
            st.warning("⚠️ Please complete model selection before proceeding.")
            return

        self.handle_navigation(clicked)

    def validate_step_completion(self) -> bool:
        """Validate that model selection is complete."""
        return self.session.has("model_selection_complete")

    def check_prerequisites(self) -> List[str]:
        """Check prerequisites for this step."""
        issues = []
        if not self.session.has("x_train"):
            issues.append("X_train data is required")
        if not self.session.has("y_train"):
            issues.append("Y_train data is required")
        if not self.session.has("x_train_selected"):
            issues.append("Selected variables are required (complete Step 4)")
        if not self.session.has("variable_selection_applied"):
            issues.append("Variable selection must be completed first")
        return issues
