"""
Step 6: Build Model for MELK Chemo Copilot

This step handles the final model building and training based on the configuration
from Step 5 (Model Selection & Cross-Validation).
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from typing import Dict, Any, List, Tuple, Optional
from steps.base_step import BaseStep
from utils.chatgpt_helper import ChatGPTHelper
from sklearn.cross_decomposition import PLSRegression
from sklearn.model_selection import cross_val_score, KFold
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.neural_network import MLPRegressor
from sklearn.svm import SVR, NuSVR
from sklearn.preprocessing import StandardScaler
from sklearn.pipeline import Pipeline
import warnings
warnings.filterwarnings('ignore')

# Optional imports for advanced models
try:
    import xgboost as xgb
    HAS_XGBOOST = True
except ImportError:
    HAS_XGBOOST = False


class Step6BuildModel(BaseStep):
    """Step 6: Build Model based on Step 5 configuration."""

    def __init__(self):
        super().__init__(step_number=6, step_name="Build Model")

    def render(self) -> None:
        """Render the model building interface."""
        # Header with help icon
        col1, col2 = st.columns([4, 1])
        with col1:
            st.markdown("## 🏗️ Step 6: Build Model")
            st.markdown("*Build and train the final model based on your configuration from Step 5*")
        with col2:
            ChatGPTHelper.create_help_icon(
                "Model Building in Chemometrics",
                "building and training chemometric models",
                """Please explain the model building process in chemometrics:

1. How to build a final model from the configuration selected in model selection?
2. What are the key steps in training a chemometric model?
3. How to validate model performance and ensure reliability?
4. What should I check before proceeding to prediction?
5. How to interpret model training results and diagnostics?

Please provide guidance for building robust chemometric models for spectroscopic data analysis."""
            )

        # Check prerequisites
        if not self._check_prerequisites():
            return

        # Get configuration from Step 5
        final_config = self.session.get("final_model_config")

        # Display configuration summary
        self._render_configuration_summary(final_config)

        # Model building interface
        self._render_model_building_interface(final_config)

    def _check_prerequisites(self) -> bool:
        """Check if all prerequisites are met."""
        missing_items = []

        if not self.session.has("x_train_selected"):
            missing_items.append("Selected training data (X_train)")
        if not self.session.has("y_train"):
            missing_items.append("Training response data (Y_train)")
        if not self.session.has("final_model_config"):
            missing_items.append("Model configuration from Step 5")

        if missing_items:
            st.error("❌ **Missing Prerequisites**")
            st.write("Please complete the following steps first:")
            for item in missing_items:
                st.write(f"• {item}")

            if st.button("🔙 Back to Step 5", key="back_to_step5"):
                self.session.set_current_step(5)
                st.rerun()
            return False

        return True

    def _render_configuration_summary(self, config: Dict[str, Any]) -> None:
        """Render the configuration summary from Step 5."""
        st.markdown("### 📋 Model Configuration Summary")
        st.markdown("*Configuration selected in Step 5: Model Selection & Cross-Validation*")

        # Create summary cards
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric(
                "Algorithm",
                config["algorithm"],
                help="Selected algorithm from Step 5"
            )

        with col2:
            route = "Manual" if config["optimization_route"] == "manual" else "Automated"
            st.metric(
                "Optimization Route",
                route,
                help="Configuration approach used"
            )

        with col3:
            if config["optimization_route"] == "manual":
                rmsecv = config["results"]["rmsecv"]
            else:
                rmsecv = config["results"]["best_rmsecv"]
            st.metric(
                "RMSECV",
                f"{rmsecv:.4f}",
                help="Root Mean Square Error of Cross-Validation"
            )

        with col4:
            if config["optimization_route"] == "manual":
                r2 = config["results"]["r2"]
            else:
                r2 = config["results"]["best_r2"]
            st.metric(
                "R²",
                f"{r2:.4f}",
                help="Coefficient of determination"
            )

        # Detailed configuration
        with st.expander("🔍 Detailed Configuration"):
            st.json(config)

    def _render_model_building_interface(self, config: Dict[str, Any]) -> None:
        """Render the model building interface."""
        st.markdown("### 🏗️ Model Building Process")

        # Get data
        x_train = self.session.get("x_train_selected")
        y_train = self.session.get("y_train")

        # Build model button
        if not self.session.has("built_model"):
            st.markdown("#### 🚀 Build Final Model")
            st.info("Click the button below to build your final model using the configuration from Step 5.")

            if st.button("🏗️ Build Model", type="primary", key="build_final_model"):
                self._build_final_model(config, x_train, y_train)
        else:
            # Show built model results
            self._render_built_model_results()

    def _build_final_model(self, config: Dict[str, Any], x_train: pd.DataFrame, y_train: pd.DataFrame) -> None:
        """Build the final model based on configuration."""
        with st.spinner("🏗️ Building final model... Please wait."):
            try:
                # Convert to numpy arrays
                x_array = x_train.select_dtypes(include=[np.number]).values
                y_array = y_train.select_dtypes(include=[np.number]).values

                if y_array.ndim == 2 and y_array.shape[1] == 1:
                    y_array = y_array.ravel()

                # Build model based on configuration
                model_results = self._create_and_train_model(config, x_array, y_array)

                # Store results
                self.session.set("built_model", model_results)

                st.success("✅ Model built successfully!")
                st.rerun()

            except Exception as e:
                st.error(f"❌ Error building model: {str(e)}")

    def _create_and_train_model(self, config: Dict[str, Any], X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """Create and train the model based on configuration."""
        algorithm = config["algorithm"]

        # Get best parameters
        if config["optimization_route"] == "manual":
            params = config.get("model_params", {})
        else:
            params = config["results"].get("best_params", {})

        # Create model based on algorithm
        if algorithm in ["NIPALS (PLS1, PLS2)", "SIMPLS"]:
            model = PLSRegression(n_components=params.get("n_components", 5))
        elif algorithm == "Multilayer Perceptron (MLP)":
            model = MLPRegressor(
                hidden_layer_sizes=params.get("hidden_layer_sizes", (100,)),
                activation=params.get("activation", "relu"),
                solver=params.get("solver", "adam"),
                alpha=params.get("alpha", 0.0001),
                max_iter=params.get("max_iter", 200),
                random_state=42
            )
        elif algorithm == "ε-Support Vector Regression (ε-SVR)":
            model = SVR(
                C=params.get("C", 1.0),
                epsilon=params.get("epsilon", 0.1),
                kernel=params.get("kernel", "rbf"),
                gamma=params.get("gamma", "scale")
            )
        elif algorithm == "Nu-Support Vector Regression (Nu-SVR)":
            model = NuSVR(
                C=params.get("C", 1.0),
                nu=params.get("nu", 0.5),
                kernel=params.get("kernel", "rbf"),
                gamma=params.get("gamma", "scale")
            )
        elif algorithm == "XGBoost" and HAS_XGBOOST:
            model = xgb.XGBRegressor(
                n_estimators=params.get("n_estimators", 100),
                max_depth=params.get("max_depth", 6),
                learning_rate=params.get("learning_rate", 0.1),
                subsample=params.get("subsample", 0.8),
                colsample_bytree=params.get("colsample_bytree", 0.8),
                reg_alpha=params.get("reg_alpha", 0),
                reg_lambda=params.get("reg_lambda", 1.0),
                random_state=42
            )
        else:
            raise ValueError(f"Unsupported algorithm: {algorithm}")

        # Train model
        model.fit(X, y)

        # Calculate performance metrics
        y_pred = model.predict(X)
        train_rmse = np.sqrt(mean_squared_error(y, y_pred))
        train_r2 = r2_score(y, y_pred)

        # Cross-validation
        cv_scores = cross_val_score(model, X, y, cv=5, scoring='neg_mean_squared_error')
        cv_rmse = np.sqrt(-cv_scores.mean())
        cv_r2_scores = cross_val_score(model, X, y, cv=5, scoring='r2')
        cv_r2 = cv_r2_scores.mean()

        return {
            "model": model,
            "algorithm": algorithm,
            "parameters": params,
            "train_rmse": train_rmse,
            "train_r2": train_r2,
            "cv_rmse": cv_rmse,
            "cv_r2": cv_r2,
            "cv_scores": cv_scores,
            "cv_r2_scores": cv_r2_scores,
            "training_data_shape": X.shape,
            "response_shape": y.shape if y.ndim > 1 else (len(y),)
        }

    def _render_built_model_results(self) -> None:
        """Render the results of the built model."""
        st.markdown("### ✅ Model Built Successfully")

        model_results = self.session.get("built_model")

        # Performance metrics
        st.markdown("#### 📊 Model Performance")

        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Training RMSE", f"{model_results['train_rmse']:.4f}")
        with col2:
            st.metric("Training R²", f"{model_results['train_r2']:.4f}")
        with col3:
            st.metric("CV RMSE", f"{model_results['cv_rmse']:.4f}")
        with col4:
            st.metric("CV R²", f"{model_results['cv_r2']:.4f}")

        # Model details
        st.markdown("#### 🔧 Model Details")
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Algorithm", model_results['algorithm'])
        with col2:
            st.metric("Training Samples", model_results['training_data_shape'][0])
        with col3:
            st.metric("Features", model_results['training_data_shape'][1])

        # Navigation to next step
        st.markdown("---")
        col1, col2, col3 = st.columns([1, 2, 1])

        with col1:
            if st.button("🔙 Back to Step 5", key="back_to_step5_from_built"):
                self.session.set_current_step(5)
                st.rerun()

        with col3:
            if st.button("➡️ Proceed to Prediction", type="primary", key="proceed_to_step7"):
                # Navigate to Step 7 (Step 6 completion is tracked by built_model)
                self.session.set_current_step(7)
                st.success("🎉 Proceeding to Step 7: Model Prediction")
                st.rerun()
