"""
Step 6: Model Prediction for MELK Chemo Copilot

This step handles model prediction and testing using the trained model from Step 5.
Since the model is already built in Step 5, this step focuses on prediction and validation.
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from typing import Dict, Any, List, Tuple, Optional
from steps.base_step import BaseStep
from utils.chatgpt_helper import Chat<PERSON>THelper
from sklearn.metrics import mean_squared_error, r2_score
import warnings
warnings.filterwarnings('ignore')


class Step6BuildModel(BaseStep):
    """Step 6: Model Prediction and Testing."""

    def __init__(self):
        super().__init__(step_number=6, step_name="Model Prediction")

    def render(self) -> None:
        """Render the model prediction interface."""
        # Header with help icon
        col1, col2 = st.columns([4, 1])
        with col1:
            st.markdown("## 🎯 Step 6: Model Prediction")
            st.markdown("*Apply your trained model to test data and predict unknown samples*")
        with col2:
            Chat<PERSON>THelper.create_help_icon(
                "Model Prediction in Chemometrics",
                "applying trained models for prediction and validation",
                """Please explain model prediction in chemometrics:

1. How to apply a trained chemometric model to test data?
2. What is the difference between training and testing performance?
3. How to interpret prediction results and performance metrics?
4. What metrics should I use to evaluate prediction accuracy?
5. How to handle prediction uncertainties and validate model reliability?

Please provide guidance for effective model prediction and result interpretation in spectroscopic analysis."""
            )

        # Check prerequisites
        if not self._check_prerequisites():
            return

        # Get the trained model from Step 5
        self._render_prediction_workflow()

    def _check_prerequisites(self) -> bool:
        """Check if all prerequisites are met."""
        missing_items = []

        if not self.session.has("x_train_selected"):
            missing_items.append("Selected training data (X_train)")
        if not self.session.has("y_train"):
            missing_items.append("Training response data (Y_train)")
        if not self.session.has("model_selection_complete"):
            missing_items.append("Completed model selection from Step 5")

        # Check if we have either final model config or selected nomination
        has_model = (self.session.has("final_model_config") or
                    self.session.has("selected_final_nomination") or
                    self.session.has("model_nominations"))

        if not has_model:
            missing_items.append("Trained model from Step 5")

        if missing_items:
            st.error("❌ **Missing Prerequisites**")
            st.write("Please complete the following steps first:")
            for item in missing_items:
                st.write(f"• {item}")

            if st.button("🔙 Back to Step 5", key="back_to_step5"):
                self.session.set_current_step(5)
                st.rerun()
            return False

        return True

    def _render_prediction_workflow(self) -> None:
        """Render the main prediction workflow."""
        # Get the trained model from Step 5
        trained_model = self._get_trained_model()

        if not trained_model:
            st.error("❌ No trained model found. Please complete Step 5 first.")
            return

        # Display model summary
        self._render_model_summary(trained_model)

        # Check for test data and handle accordingly
        self._render_test_data_section(trained_model)

        # Show model performance plots and results
        self._render_model_performance_section(trained_model)

        # Navigation
        self._render_navigation()

    def _get_trained_model(self) -> Optional[Dict[str, Any]]:
        """Get the trained model from Step 5 session data."""
        # Try to get from selected nomination first
        selected_nomination = self.session.get("selected_final_nomination")
        if selected_nomination:
            return {
                "model": selected_nomination.get("trained_model"),
                "algorithm": selected_nomination["algorithm"],
                "parameters": selected_nomination["model_params"],
                "rmsecv": selected_nomination["overall_rmsecv"],
                "r2": selected_nomination["overall_r2"],
                "compound_results": selected_nomination["compound_results"]
            }

        # Try to get from final model config
        final_config = self.session.get("final_model_config")
        if final_config:
            return {
                "model": self.session.get("final_model"),
                "algorithm": final_config["algorithm"],
                "parameters": final_config.get("model_params", {}),
                "rmsecv": final_config["results"].get("rmsecv", final_config["results"].get("best_rmsecv", 0)),
                "r2": final_config["results"].get("r2", final_config["results"].get("train_r2", 0)),
                "compound_results": final_config["results"].get("component_results", {})
            }

        # Try to get from model training results (fallback)
        model_results = self.session.get("model_training_results")
        if model_results:
            results = model_results.get("results", {})
            return {
                "model": results.get("best_model", results.get("model")),
                "algorithm": model_results["algorithm"],
                "parameters": model_results.get("model_params", {}),
                "rmsecv": results.get("rmsecv", results.get("best_rmsecv", 0)),
                "r2": results.get("r2", results.get("train_r2", 0)),
                "compound_results": results.get("component_results", {})
            }

        return None

    def _render_model_summary(self, trained_model: Dict[str, Any]) -> None:
        """Render summary of the trained model."""
        st.markdown("### 📋 Trained Model Summary")
        st.markdown("*Model selected and trained in Step 5*")

        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Algorithm", trained_model["algorithm"])
        with col2:
            st.metric("RMSECV", f"{trained_model['rmsecv']:.4f}")
        with col3:
            st.metric("Training R²", f"{trained_model['r2']:.4f}")
        with col4:
            # Count parameters
            param_count = len(trained_model.get("parameters", {}))
            st.metric("Parameters", param_count)

        # Show individual compound results if available
        compound_results = trained_model.get("compound_results", {})
        if compound_results and isinstance(compound_results, dict):
            st.markdown("#### 📊 Individual Compound Performance")

            # Create columns for each compound
            compounds = list(compound_results.keys())
            if len(compounds) <= 4:
                cols = st.columns(len(compounds))
                for i, (compound, results) in enumerate(compound_results.items()):
                    with cols[i]:
                        if isinstance(results, dict):
                            rmsecv = results.get('rmsecv', 0)
                            r2 = results.get('r2', 0)
                            st.metric(
                                f"{compound}",
                                f"RMSECV: {rmsecv:.4f}",
                                f"R²: {r2:.4f}"
                            )

    def _render_test_data_section(self, trained_model: Dict[str, Any]) -> None:
        """Render test data section - upload if not available, or show summary if available."""
        st.markdown("### 🧪 Test Data Validation")

        # Check if test data is available
        has_x_test = self.session.has("x_test") or self.session.has("x_test_selected")
        has_y_test = self.session.has("y_test")

        if not has_x_test or not has_y_test:
            # Test data upload section
            st.markdown("#### 📤 Upload Test Data")
            st.info("Upload test data to validate your model's performance on unseen samples.")

            col1, col2 = st.columns(2)

            with col1:
                st.markdown("**X_test (Spectroscopic Data)**")
                x_test_file = st.file_uploader(
                    "Upload X_test file",
                    type=['csv', 'xlsx', 'xls'],
                    help="Upload CSV or Excel file with test spectroscopic data",
                    key="x_test_upload"
                )

            with col2:
                st.markdown("**Y_test (Reference Values)**")
                y_test_file = st.file_uploader(
                    "Upload Y_test file",
                    type=['csv', 'xlsx', 'xls'],
                    help="Upload CSV or Excel file with test reference values",
                    key="y_test_upload"
                )

            # Process uploaded files
            if x_test_file is not None and y_test_file is not None:
                if st.button("📥 Load Test Data", type="primary", key="load_test_data"):
                    self._process_test_data_upload(x_test_file, y_test_file)
        else:
            # Show test data summary and prediction
            self._render_test_data_prediction(trained_model)

    def _process_test_data_upload(self, x_test_file, y_test_file) -> None:
        """Process uploaded test data files."""
        try:
            # Read X_test - handle both CSV and Excel files
            x_test = self._read_data_file(x_test_file, "X_test")
            st.success(f"✅ Loaded X_test: {x_test.shape[0]} samples × {x_test.shape[1]} variables")

            # Read Y_test - handle both CSV and Excel files
            y_test = self._read_data_file(y_test_file, "Y_test")

            # Count actual numeric compounds in Y_test
            y_test_numeric = y_test.select_dtypes(include=[np.number])
            actual_compounds = y_test_numeric.shape[1]
            st.success(f"✅ Loaded Y_test: {y_test.shape[0]} samples × {actual_compounds} compounds")

            # Apply same preprocessing as training data
            x_test_processed = self._apply_preprocessing_to_test_data(x_test)

            # Store in session
            self.session.set("x_test", x_test)
            self.session.set("x_test_selected", x_test_processed)
            self.session.set("y_test", y_test)

            st.success("🎉 Test data loaded and preprocessed successfully!")
            st.rerun()

        except Exception as e:
            st.error(f"❌ Error loading test data: {str(e)}")
            st.write("Please check that your files are properly formatted and contain numeric data.")

    def _read_data_file(self, uploaded_file, file_type: str) -> pd.DataFrame:
        """Read data file - supports CSV and Excel formats."""
        file_extension = uploaded_file.name.split('.')[-1].lower()

        try:
            if file_extension == 'csv':
                # Try different CSV formats
                try:
                    # Try semicolon separator with comma decimal
                    data = pd.read_csv(uploaded_file, sep=';', decimal=',')
                except:
                    uploaded_file.seek(0)  # Reset file pointer
                    try:
                        # Try comma separator with dot decimal
                        data = pd.read_csv(uploaded_file, sep=',', decimal='.')
                    except:
                        uploaded_file.seek(0)  # Reset file pointer
                        # Try tab separator
                        data = pd.read_csv(uploaded_file, sep='\t')

            elif file_extension in ['xlsx', 'xls']:
                # Read Excel file
                data = pd.read_excel(uploaded_file, engine='openpyxl' if file_extension == 'xlsx' else 'xlrd')

            else:
                raise ValueError(f"Unsupported file format: {file_extension}")

            # Basic validation
            if data.empty:
                raise ValueError(f"{file_type} file is empty")

            # Check for numeric data
            numeric_cols = data.select_dtypes(include=[np.number]).columns
            if len(numeric_cols) == 0:
                raise ValueError(f"{file_type} file contains no numeric data")

            return data

        except Exception as e:
            raise Exception(f"Error reading {file_type} file: {str(e)}")

    def _apply_preprocessing_to_test_data(self, x_test: pd.DataFrame) -> pd.DataFrame:
        """Apply the exact same preprocessing pipeline used on training data to test data."""
        st.info("🔄 Applying comprehensive preprocessing to test data...")

        # Step 1: Extract numeric data and ensure string column names
        x_test_numeric = x_test.select_dtypes(include=[np.number]).copy()
        if x_test_numeric.empty:
            raise ValueError("No numeric columns found in test data")

        # Fix column names to be strings to avoid mixed type issues
        x_test_numeric.columns = x_test_numeric.columns.astype(str)
        st.info(f"📊 Raw test data: {x_test_numeric.shape[0]} samples × {x_test_numeric.shape[1]} variables")

        # Step 2: Match columns with training data structure
        x_train_selected = self.session.get("x_train_selected")
        if x_train_selected is not None:
            training_columns = list(x_train_selected.columns)
            st.info(f"📋 Training data structure: {len(training_columns)} variables")

            # Try to match columns with training data
            common_columns = [col for col in training_columns if col in x_test_numeric.columns]

            if len(common_columns) > 0:
                # Use common columns
                x_test_aligned = x_test_numeric[common_columns].copy()
                st.success(f"✅ Matched {len(common_columns)} variables with training data")

                if len(common_columns) < len(training_columns):
                    missing_vars = len(training_columns) - len(common_columns)
                    st.warning(f"⚠️ {missing_vars} variables from training data not found in test data")
            else:
                # No column name match - use positional matching
                if x_test_numeric.shape[1] >= len(training_columns):
                    # Use first N columns where N is the number of training variables
                    x_test_aligned = x_test_numeric.iloc[:, :len(training_columns)].copy()
                    x_test_aligned.columns = training_columns  # Rename to match training
                    st.warning(f"⚠️ No column name match. Using first {len(training_columns)} columns from test data")
                else:
                    raise ValueError(f"Test data has {x_test_numeric.shape[1]} variables but training data has {len(training_columns)} variables")
        else:
            x_test_aligned = x_test_numeric.copy()
            st.warning("⚠️ No training data reference found. Using all numeric columns from test data.")

        # Step 3: Apply the same preprocessing method used on training data
        preprocessing_results = self.session.get("preprocessing_results", {})
        if preprocessing_results and "selected_method" in preprocessing_results:
            preprocessing_method = preprocessing_results["selected_method"]
            st.info(f"🧪 Applying preprocessing method: {preprocessing_method}")

            # Import preprocessing functions
            from utils.preprocessing import preprocess_data

            try:
                x_test_preprocessed = preprocess_data(x_test_aligned, preprocessing_method)

                # Ensure preprocessed data has string column names
                if hasattr(x_test_preprocessed, 'columns'):
                    x_test_preprocessed.columns = x_test_preprocessed.columns.astype(str)

                st.success(f"✅ Applied {preprocessing_method} preprocessing")
            except Exception as e:
                st.warning(f"⚠️ Error applying preprocessing: {str(e)}. Using aligned data without preprocessing.")
                x_test_preprocessed = x_test_aligned
        else:
            st.info("ℹ️ No preprocessing method found. Using aligned data without preprocessing.")
            x_test_preprocessed = x_test_aligned

        # Step 4: Apply variable selection if available
        selected_variables = self.session.get("selected_variables")
        if selected_variables is not None and len(selected_variables) > 0:
            # Convert to string if needed
            selected_variables = [str(var) for var in selected_variables]

            # Filter to selected variables
            available_vars = [var for var in selected_variables if var in x_test_preprocessed.columns]
            if len(available_vars) > 0:
                x_test_final = x_test_preprocessed[available_vars].copy()
                st.success(f"✅ Applied variable selection: {len(available_vars)} variables selected")
            else:
                st.warning("⚠️ No selected variables found in test data. Using all preprocessed variables.")
                x_test_final = x_test_preprocessed
        else:
            st.info("ℹ️ No variable selection applied. Using all preprocessed variables.")
            x_test_final = x_test_preprocessed

        # Step 5: Final validation and alignment check
        if x_test_final.shape[1] == 0:
            raise ValueError("No variables remaining after preprocessing. Please check your test data format.")

        # Ensure final data matches training data structure exactly
        if x_train_selected is not None:
            if x_test_final.shape[1] != x_train_selected.shape[1]:
                st.error(f"❌ Dimension mismatch: Test data has {x_test_final.shape[1]} variables, training data has {x_train_selected.shape[1]} variables")

                # Try to fix by selecting matching columns
                training_cols = list(x_train_selected.columns)
                test_cols = list(x_test_final.columns)
                matching_cols = [col for col in training_cols if col in test_cols]

                if len(matching_cols) == len(training_cols):
                    x_test_final = x_test_final[training_cols]  # Reorder to match training
                    st.success(f"✅ Fixed dimension mismatch by reordering columns")
                else:
                    raise ValueError(f"Cannot align test data with training data. Missing columns: {set(training_cols) - set(test_cols)}")

        st.success(f"🎉 Test data preprocessing complete: {x_test_final.shape[0]} samples × {x_test_final.shape[1]} variables")
        st.info(f"📋 Final test data columns: {list(x_test_final.columns)[:5]}..." if len(x_test_final.columns) > 5 else f"📋 Final test data columns: {list(x_test_final.columns)}")

        return x_test_final

    def _render_test_data_prediction(self, trained_model: Dict[str, Any]) -> None:
        """Render test data prediction and results."""
        st.markdown("#### 📊 Test Data Summary")

        # Get test data
        x_test = self.session.get("x_test_selected")
        y_test = self.session.get("y_test")

        if x_test is not None and y_test is not None:
            # Count actual numeric compounds in Y_test
            y_test_numeric = y_test.select_dtypes(include=[np.number])
            actual_compounds = y_test_numeric.shape[1]

            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("Test Samples", x_test.shape[0])
            with col2:
                st.metric("Variables", x_test.shape[1])
            with col3:
                st.metric("Compounds", actual_compounds)

            # Predict test data if not already done
            if not self.session.has("test_predictions"):
                if st.button("🧪 Predict Test Data", type="primary", key="predict_test_data"):
                    self._predict_test_data(trained_model)
            else:
                # Show test prediction results
                self._render_test_prediction_results()

    def _predict_test_data(self, trained_model: Dict[str, Any]) -> None:
        """Predict on test data using the trained model."""
        with st.spinner("🧪 Predicting test data..."):
            try:
                x_test = self.session.get("x_test_selected")
                y_test = self.session.get("y_test")
                model = trained_model["model"]

                if model is None:
                    st.error("❌ Trained model not found. Please retrain the model in Step 5.")
                    return

                # Convert to numpy arrays
                x_test_array = x_test.select_dtypes(include=[np.number]).values
                y_test_array = y_test.select_dtypes(include=[np.number]).values

                if y_test_array.ndim == 2 and y_test_array.shape[1] == 1:
                    y_test_array = y_test_array.ravel()

                # Make predictions
                y_pred = model.predict(x_test_array)

                # Calculate metrics
                if y_test_array.ndim == 1 or y_test_array.shape[1] == 1:
                    # Single component
                    test_rmse = np.sqrt(mean_squared_error(y_test_array, y_pred))
                    test_r2 = r2_score(y_test_array, y_pred)

                    # Calculate relative values (percentage)
                    relative_errors = np.abs((y_test_array - y_pred) / y_test_array) * 100

                    test_results = {
                        "y_true": y_test_array,
                        "y_pred": y_pred,
                        "test_rmse": test_rmse,
                        "test_r2": test_r2,
                        "relative_errors": relative_errors,
                        "sample_names": x_test.index.tolist() if hasattr(x_test, 'index') else list(range(len(x_test))),
                        "compound_names": list(y_test.columns) if hasattr(y_test, 'columns') else ["Component_1"]
                    }
                else:
                    # Multi-component - use only numeric columns for compound names
                    y_test_numeric = y_test.select_dtypes(include=[np.number])
                    compound_names = list(y_test_numeric.columns)

                    # Ensure we don't exceed the actual number of compounds in the data
                    n_actual_compounds = y_test_array.shape[1]
                    compound_names = compound_names[:n_actual_compounds]

                    st.info(f"🔍 Processing {len(compound_names)} compounds: {compound_names}")

                    # Calculate metrics for each component
                    component_results = {}
                    for i, compound in enumerate(compound_names):
                        if i < y_test_array.shape[1]:  # Safety check
                            y_true_comp = y_test_array[:, i]
                            y_pred_comp = y_pred[:, i] if y_pred.ndim > 1 and i < y_pred.shape[1] else y_pred

                            rmse_comp = np.sqrt(mean_squared_error(y_true_comp, y_pred_comp))
                            r2_comp = r2_score(y_true_comp, y_pred_comp)
                            rel_errors_comp = np.abs((y_true_comp - y_pred_comp) / y_true_comp) * 100

                            component_results[compound] = {
                                "rmse": rmse_comp,
                                "r2": r2_comp,
                                "relative_errors": rel_errors_comp
                            }

                    test_results = {
                        "y_true": y_test_array,
                        "y_pred": y_pred,
                        "component_results": component_results,
                        "sample_names": x_test.index.tolist() if hasattr(x_test, 'index') else list(range(len(x_test))),
                        "compound_names": compound_names
                    }

                # Store results
                self.session.set("test_predictions", test_results)
                st.success("✅ Test data prediction completed!")
                st.rerun()

            except Exception as e:
                st.error(f"❌ Error predicting test data: {str(e)}")
                st.write("Please check that the test data has the same structure as the training data.")

    def _render_test_prediction_results(self) -> None:
        """Render test prediction results with plots and tables."""
        test_results = self.session.get("test_predictions")

        st.markdown("#### 📈 Test Prediction Results")

        # Check if single or multi-component
        if "component_results" in test_results:
            # Multi-component results
            self._render_multicomponent_results(test_results)
        else:
            # Single component results
            self._render_single_component_results(test_results)

    def _render_single_component_results(self, test_results: Dict[str, Any]) -> None:
        """Render results for single component prediction."""
        # Metrics
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("RMSEP", f"{test_results['test_rmse']:.4f}")
        with col2:
            st.metric("Test R²", f"{test_results['test_r2']:.4f}")
        with col3:
            st.metric("Test Samples", len(test_results['y_true']))
        with col4:
            mean_rel_error = np.mean(test_results['relative_errors'])
            st.metric("Mean Rel. Error (%)", f"{mean_rel_error:.2f}")

        # Prediction plot
        self._create_prediction_plot(test_results)

        # Prediction table
        self._create_prediction_table(test_results)

    def _render_multicomponent_results(self, test_results: Dict[str, Any]) -> None:
        """Render results for multi-component prediction."""
        component_results = test_results["component_results"]
        compound_names = test_results["compound_names"]

        # Overall metrics
        st.markdown("##### 📊 Overall Performance Metrics")
        cols = st.columns(len(compound_names))
        for i, (compound, results) in enumerate(component_results.items()):
            with cols[i]:
                st.metric(
                    f"{compound}",
                    f"RMSEP: {results['rmse']:.4f}",
                    f"R²: {results['r2']:.4f}"
                )

        # Individual component plots
        self._create_multicomponent_plots(test_results)

        # Detailed prediction table
        self._create_multicomponent_table(test_results)

    def _create_prediction_plot(self, test_results: Dict[str, Any]) -> None:
        """Create prediction vs actual plot for single component."""
        y_true = test_results['y_true']
        y_pred = test_results['y_pred']

        fig = go.Figure()

        # Scatter plot
        fig.add_trace(go.Scatter(
            x=y_true,
            y=y_pred,
            mode='markers',
            name='Predictions',
            marker=dict(
                size=8,
                color='rgba(25, 118, 210, 0.7)',
                line=dict(width=1, color='rgba(25, 118, 210, 1)')
            ),
            text=test_results['sample_names'],
            hovertemplate='<b>%{text}</b><br>Actual: %{x:.3f}<br>Predicted: %{y:.3f}<extra></extra>'
        ))

        # Perfect prediction line
        min_val = min(min(y_true), min(y_pred))
        max_val = max(max(y_true), max(y_pred))
        fig.add_trace(go.Scatter(
            x=[min_val, max_val],
            y=[min_val, max_val],
            mode='lines',
            name='Perfect Prediction',
            line=dict(color='red', dash='dash', width=2)
        ))

        fig.update_layout(
            title=f"Predicted vs Actual Values (R² = {test_results['test_r2']:.3f})",
            xaxis_title="Actual Values",
            yaxis_title="Predicted Values",
            font=dict(family="Nunito Sans", size=12),
            showlegend=True,
            height=500
        )

        st.plotly_chart(fig, use_container_width=True)

    def _create_multicomponent_plots(self, test_results: Dict[str, Any]) -> None:
        """Create plots for multi-component predictions."""
        component_results = test_results["component_results"]
        compound_names = test_results["compound_names"]
        y_true = test_results['y_true']
        y_pred = test_results['y_pred']

        # Create subplots for each component
        for i, compound in enumerate(compound_names):
            if i < y_true.shape[1]:  # Safety check to prevent index out of bounds
                st.markdown(f"##### 📈 {compound} Prediction Plot")

                y_true_comp = y_true[:, i]
                y_pred_comp = y_pred[:, i] if y_pred.ndim > 1 and i < y_pred.shape[1] else y_pred

                fig = go.Figure()

                # Scatter plot
                fig.add_trace(go.Scatter(
                    x=y_true_comp,
                    y=y_pred_comp,
                    mode='markers',
                    name=f'{compound} Predictions',
                    marker=dict(
                        size=8,
                        color=f'rgba({25 + i*50}, {118 + i*30}, {210 - i*40}, 0.7)',
                        line=dict(width=1, color=f'rgba({25 + i*50}, {118 + i*30}, {210 - i*40}, 1)')
                    ),
                    text=test_results['sample_names'],
                    hovertemplate=f'<b>%{{text}}</b><br>Actual: %{{x:.3f}}<br>Predicted: %{{y:.3f}}<extra></extra>'
                ))

                # Perfect prediction line
                min_val = min(min(y_true_comp), min(y_pred_comp))
                max_val = max(max(y_true_comp), max(y_pred_comp))
                fig.add_trace(go.Scatter(
                    x=[min_val, max_val],
                    y=[min_val, max_val],
                    mode='lines',
                    name='Perfect Prediction',
                    line=dict(color='red', dash='dash', width=2)
                ))

                r2_comp = component_results[compound]['r2']
                fig.update_layout(
                    title=f"{compound}: Predicted vs Actual (R² = {r2_comp:.3f})",
                    xaxis_title="Actual Values",
                    yaxis_title="Predicted Values",
                    font=dict(family="Nunito Sans", size=12),
                    showlegend=True,
                    height=400
                )

                st.plotly_chart(fig, use_container_width=True)

    def _create_prediction_table(self, test_results: Dict[str, Any]) -> None:
        """Create prediction results table for single component."""
        st.markdown("##### 📋 Detailed Prediction Results")

        # Create DataFrame
        results_df = pd.DataFrame({
            'Sample': test_results['sample_names'],
            'Actual': test_results['y_true'],
            'Predicted': test_results['y_pred'],
            'Residual': test_results['y_true'] - test_results['y_pred'],
            'Abs Error': np.abs(test_results['y_true'] - test_results['y_pred']),
            'Relative Error (%)': test_results['relative_errors']
        })

        # Format numbers
        for col in ['Actual', 'Predicted', 'Residual', 'Abs Error']:
            results_df[col] = results_df[col].round(4)
        results_df['Relative Error (%)'] = results_df['Relative Error (%)'].round(2)

        st.dataframe(results_df, use_container_width=True)

        # Download button
        csv = results_df.to_csv(index=False)
        st.download_button(
            label="📥 Download Prediction Results",
            data=csv,
            file_name="test_prediction_results.csv",
            mime="text/csv"
        )

    def _create_multicomponent_table(self, test_results: Dict[str, Any]) -> None:
        """Create prediction results table for multi-component."""
        st.markdown("##### 📋 Detailed Multi-Component Prediction Results")

        compound_names = test_results["compound_names"]
        y_true = test_results['y_true']
        y_pred = test_results['y_pred']
        component_results = test_results["component_results"]

        # Create comprehensive DataFrame
        data = {'Sample': test_results['sample_names']}

        for i, compound in enumerate(compound_names):
            if i < y_true.shape[1]:  # Safety check to prevent index out of bounds
                y_true_comp = y_true[:, i]
                y_pred_comp = y_pred[:, i] if y_pred.ndim > 1 and i < y_pred.shape[1] else y_pred
                residuals = y_true_comp - y_pred_comp
                rel_errors = component_results[compound]['relative_errors']

                data[f'{compound}_Actual'] = y_true_comp.round(4)
                data[f'{compound}_Predicted'] = y_pred_comp.round(4)
                data[f'{compound}_Residual'] = residuals.round(4)
                data[f'{compound}_RelError(%)'] = rel_errors.round(2)

        results_df = pd.DataFrame(data)
        st.dataframe(results_df, use_container_width=True)

        # Download button
        csv = results_df.to_csv(index=False)
        st.download_button(
            label="📥 Download Multi-Component Results",
            data=csv,
            file_name="multicomponent_prediction_results.csv",
            mime="text/csv"
        )

    def _render_model_performance_section(self, trained_model: Dict[str, Any]) -> None:
        """Render model performance plots and additional analysis."""
        if self.session.has("test_predictions"):
            st.markdown("### 📊 Model Performance Analysis")
            st.info("✅ Model validation completed using test data. Review the results above.")
        else:
            st.markdown("### 📊 Model Performance Analysis")
            st.info("🔄 Upload and predict test data to see comprehensive model performance analysis.")

    def _render_navigation(self) -> None:
        """Render navigation buttons."""
        st.markdown("---")

        # Use base class navigation with summary support
        clicked = self.render_navigation_buttons(
            show_previous=True,
            next_enabled=True,
            custom_next_text="Next: Model Report →",
            custom_previous_text="← Previous: Model Selection"
        )

        self.handle_navigation(clicked)
