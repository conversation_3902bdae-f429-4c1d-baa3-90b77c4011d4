"""
ChatGPT Helper for MELK Chemo Copilot
Provides context-aware help integration with ChatGPT
"""

import streamlit as st
import urllib.parse


class ChatGPTHelper:
    """Helper class for ChatGPT integration with context-aware prompts."""

    @staticmethod
    def _get_comprehensive_context() -> str:
        """Get comprehensive context from the current session state."""
        try:
            # Get session state if available
            if hasattr(st, 'session_state'):
                context_parts = []

                # Data information
                if 'x_train' in st.session_state:
                    x_train = st.session_state['x_train']
                    y_train = st.session_state.get('y_train')
                    context_parts.append(f"Training data: {x_train.shape[0]} samples, {x_train.shape[1]} variables")
                    if y_train is not None:
                        context_parts.append(f"Response data: {y_train.shape}")

                if 'x_test' in st.session_state:
                    x_test = st.session_state['x_test']
                    context_parts.append(f"Test data: {x_test.shape[0]} samples, {x_test.shape[1]} variables")

                # Completed steps
                completed_steps = []
                if st.session_state.get('data_upload_complete'):
                    completed_steps.append("Step 1: Data Upload")
                if st.session_state.get('data_overview_complete'):
                    completed_steps.append("Step 2: Data Overview")
                if st.session_state.get('preprocessing_complete'):
                    completed_steps.append("Step 3: Preprocessing")
                    if 'preprocessing_method_name' in st.session_state:
                        context_parts.append(f"Preprocessing method: {st.session_state['preprocessing_method_name']}")
                if st.session_state.get('variable_selection_complete'):
                    completed_steps.append("Step 4: Variable Selection")
                    if 'selected_variable_method' in st.session_state:
                        context_parts.append(f"Variable selection: {st.session_state['selected_variable_method']}")
                if st.session_state.get('model_selection_complete'):
                    completed_steps.append("Step 5: Model Selection")
                if st.session_state.get('model_building_complete'):
                    completed_steps.append("Step 6: Model Building")
                if st.session_state.get('prediction_complete'):
                    completed_steps.append("Step 7: Model Prediction")

                if completed_steps:
                    context_parts.append(f"Completed steps: {', '.join(completed_steps)}")

                # Current step
                if 'current_step' in st.session_state:
                    current_step = st.session_state['current_step']
                    step_names = {
                        1: "Data Upload", 2: "Data Overview", 3: "Preprocessing",
                        4: "Variable Selection", 5: "Model Selection", 6: "Model Building",
                        7: "Model Prediction", 8: "Model Report"
                    }
                    context_parts.append(f"Current step: Step {current_step}: {step_names.get(current_step, 'Unknown')}")

                # Recommended components
                if 'recommended_components' in st.session_state:
                    context_parts.append(f"Recommended PLS components: {st.session_state['recommended_components']}")

                # Model information
                if 'built_model' in st.session_state:
                    model_info = st.session_state['built_model']
                    context_parts.append(f"Built model: {model_info.get('algorithm', 'Unknown')} with CV RMSE: {model_info.get('cv_rmse', 'N/A')}")

                return "\n".join(context_parts) if context_parts else ""

        except Exception:
            pass

        return ""

    @staticmethod
    def create_help_icon(topic: str, context: str = "", additional_info: str = "") -> None:
        """
        Create a help icon that opens ChatGPT with a context-aware prompt.

        Parameters:
        -----------
        topic : str
            The main topic for the help query
        context : str
            Additional context about the current application state
        additional_info : str
            Any additional information to include in the prompt
        """
        # Get comprehensive context
        comprehensive_context = ChatGPTHelper._get_comprehensive_context()

        # Create the base prompt
        base_prompt = f"""I'm using the MELK Chemo Copilot, a chemometric analysis tool for spectroscopic data analysis using PLS regression.

I need help with: {topic}

Context: I'm currently working on {context}

Current Application State:
{comprehensive_context}

{additional_info}

Please provide a clear, practical explanation suitable for someone working with chemometric data analysis."""

        # URL encode the prompt
        encoded_prompt = urllib.parse.quote(base_prompt)

        # Create ChatGPT URL
        chatgpt_url = f"https://chat.openai.com/?q={encoded_prompt}"

        # Create help button with ChatGPT icon
        help_html = f"""
        <a href="{chatgpt_url}" target="_blank" style="text-decoration: none;">
            <div style="
                display: inline-block;
                background: linear-gradient(135deg, #10a37f 0%, #1a7f64 100%);
                border-radius: 6px;
                width: 24px;
                height: 24px;
                text-align: center;
                line-height: 24px;
                margin-left: 8px;
                cursor: pointer;
                box-shadow: 0 2px 4px rgba(0,0,0,0.2);
                transition: all 0.3s ease;
                position: relative;
            " title="Get help with ChatGPT">
                <svg width="16" height="16" viewBox="0 0 24 24" style="
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    fill: white;
                ">
                    <path d="M22.2819 9.8211a5.9847 5.9847 0 0 0-.5157-4.9108 6.0462 6.0462 0 0 0-6.5098-2.9A6.0651 6.0651 0 0 0 4.9807 4.1818a5.9847 5.9847 0 0 0-3.9977 2.9 6.0462 6.0462 0 0 0 .7427 7.0966 5.98 5.98 0 0 0 .511 4.9107 6.051 6.051 0 0 0 6.5146 2.9001A5.9847 5.9847 0 0 0 13.2599 24a6.0557 6.0557 0 0 0 5.7718-4.2058 5.9894 5.9894 0 0 0 3.9977-2.9001 6.0557 6.0557 0 0 0-.7475-7.0729zm-9.022 12.6081a4.4755 4.4755 0 0 1-2.8764-1.0408l.1419-.0804 4.7783-2.7582a.7948.7948 0 0 0 .3927-.6813v-6.7369l2.02 1.1686a.071.071 0 0 1 .038.052v5.5826a4.504 4.504 0 0 1-4.4945 4.4944zm-9.6607-4.1254a4.4708 4.4708 0 0 1-.5346-3.0137l.142.0852 4.783 2.7582a.7712.7712 0 0 0 .7806 0l5.8428-3.3685v2.3324a.0804.0804 0 0 1-.0332.0615L9.74 19.9502a4.4992 4.4992 0 0 1-6.1408-1.6464zM2.3408 7.8956a4.485 4.485 0 0 1 2.3655-1.9728V11.6a.7664.7664 0 0 0 .3879.6765l5.8144 3.3543-2.0201 1.1685a.0757.0757 0 0 1-.071 0l-4.8303-2.7865A4.504 4.504 0 0 1 2.3408 7.872zm16.5963 3.8558L13.1038 8.364 15.1192 7.2a.0757.0757 0 0 1 .071 0l4.8303 2.7913a4.4944 4.4944 0 0 1-.6765 8.1042v-5.6772a.79.79 0 0 0-.407-.667zm2.0107-3.0231l-.142-.0852-4.7735-2.7818a.7759.7759 0 0 0-.7854 0L9.409 9.2297V6.8974a.0662.0662 0 0 1 .0284-.0615l4.8303-2.7866a4.4992 4.4992 0 0 1 6.6802 4.66zM8.3065 12.863l-2.02-1.1638a.0804.0804 0 0 1-.038-.0567V6.0742a4.4992 4.4992 0 0 1 7.3757-3.4537l-.142.0805L8.704 5.459a.7948.7948 0 0 0-.3927.6813zm1.0976-2.3654l2.602-1.4998 2.6069 1.4998v2.9994l-2.5974 1.4997-2.6067-1.4997Z"/>
                </svg>
            </div>
        </a>
        """

        st.markdown(help_html, unsafe_allow_html=True)

    @staticmethod
    def create_inline_help_icon(topic: str, context: str = "", additional_info: str = "") -> str:
        """
        Create an inline help icon that can be embedded next to titles.
        Returns HTML string to be used with st.markdown.

        Parameters:
        -----------
        topic : str
            The main topic for the help query
        context : str
            Additional context about the current application state
        additional_info : str
            Any additional information to include in the prompt
        """
        # Get comprehensive context
        comprehensive_context = ChatGPTHelper._get_comprehensive_context()

        # Create the base prompt
        base_prompt = f"""I'm using the MELK Chemo Copilot, a chemometric analysis tool for spectroscopic data analysis using PLS regression.

I need help with: {topic}

Context: I'm currently working on {context}

Current Application State:
{comprehensive_context}

{additional_info}

Please provide a clear, practical explanation suitable for someone working with chemometric data analysis."""

        # URL encode the prompt
        encoded_prompt = urllib.parse.quote(base_prompt)

        # Create ChatGPT URL
        chatgpt_url = f"https://chat.openai.com/?q={encoded_prompt}"

        # Create inline help icon HTML
        help_html = f"""
        <a href="{chatgpt_url}" target="_blank" style="text-decoration: none; margin-left: 8px;">
            <div style="
                display: inline-block;
                background: linear-gradient(135deg, #10a37f 0%, #1a7f64 100%);
                border-radius: 4px;
                width: 20px;
                height: 20px;
                text-align: center;
                line-height: 20px;
                cursor: pointer;
                box-shadow: 0 1px 3px rgba(0,0,0,0.2);
                transition: all 0.3s ease;
                position: relative;
                vertical-align: middle;
            " title="Get help with ChatGPT">
                <svg width="12" height="12" viewBox="0 0 24 24" style="
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    fill: white;
                ">
                    <path d="M22.2819 9.8211a5.9847 5.9847 0 0 0-.5157-4.9108 6.0462 6.0462 0 0 0-6.5098-2.9A6.0651 6.0651 0 0 0 4.9807 4.1818a5.9847 5.9847 0 0 0-3.9977 2.9 6.0462 6.0462 0 0 0 .7427 7.0966 5.98 5.98 0 0 0 .511 4.9107 6.051 6.051 0 0 0 6.5146 2.9001A5.9847 5.9847 0 0 0 13.2599 24a6.0557 6.0557 0 0 0 5.7718-4.2058 5.9894 5.9894 0 0 0 3.9977-2.9001 6.0557 6.0557 0 0 0-.7475-7.0729zm-9.022 12.6081a4.4755 4.4755 0 0 1-2.8764-1.0408l.1419-.0804 4.7783-2.7582a.7948.7948 0 0 0 .3927-.6813v-6.7369l2.02 1.1686a.071.071 0 0 1 .038.052v5.5826a4.504 4.504 0 0 1-4.4945 4.4944zm-9.6607-4.1254a4.4708 4.4708 0 0 1-.5346-3.0137l.142.0852 4.783 2.7582a.7712.7712 0 0 0 .7806 0l5.8428-3.3685v2.3324a.0804.0804 0 0 1-.0332.0615L9.74 19.9502a4.4992 4.4992 0 0 1-6.1408-1.6464zM2.3408 7.8956a4.485 4.485 0 0 1 2.3655-1.9728V11.6a.7664.7664 0 0 0 .3879.6765l5.8144 3.3543-2.0201 1.1685a.0757.0757 0 0 1-.071 0l-4.8303-2.7865A4.504 4.504 0 0 1 2.3408 7.872zm16.5963 3.8558L13.1038 8.364 15.1192 7.2a.0757.0757 0 0 1 .071 0l4.8303 2.7913a4.4944 4.4944 0 0 1-.6765 8.1042v-5.6772a.79.79 0 0 0-.407-.667zm2.0107-3.0231l-.142-.0852-4.7735-2.7818a.7759.7759 0 0 0-.7854 0L9.409 9.2297V6.8974a.0662.0662 0 0 1 .0284-.0615l4.8303-2.7866a4.4992 4.4992 0 0 1 6.6802 4.66zM8.3065 12.863l-2.02-1.1638a.0804.0804 0 0 1-.038-.0567V6.0742a4.4992 4.4992 0 0 1 7.3757-3.4537l-.142.0805L8.704 5.459a.7948.7948 0 0 0-.3927.6813zm1.0976-2.3654l2.602-1.4998 2.6069 1.4998v2.9994l-2.5974 1.4997-2.6067-1.4997Z"/>
                </svg>
            </div>
        </a>
        """

        return help_html

    @staticmethod
    def create_title_with_help(title: str, topic: str, context: str = "", additional_info: str = "",
                              title_level: str = "###") -> None:
        """
        Create a title with an adjacent ChatGPT help icon.

        Parameters:
        -----------
        title : str
            The title text
        topic : str
            The main topic for the help query
        context : str
            Additional context about the current application state
        additional_info : str
            Any additional information to include in the prompt
        title_level : str
            Markdown title level (e.g., "###", "####", "#####")
        """
        help_icon = ChatGPTHelper.create_inline_help_icon(topic, context, additional_info)

        title_html = f"""
        <div style="display: flex; align-items: center; margin-bottom: 1rem;">
            <{title_level.replace('#', 'h').replace('h', 'h' + str(len(title_level)))} style="margin: 0; margin-right: 8px;">
                {title}
            </{title_level.replace('#', 'h').replace('h', 'h' + str(len(title_level)))}>
            {help_icon}
        </div>
        """

        st.markdown(title_html, unsafe_allow_html=True)

    @staticmethod
    def create_help_button(topic: str, context: str = "", additional_info: str = "",
                          button_text: str = "🤖 Ask ChatGPT") -> None:
        """
        Create a help button that opens ChatGPT with a context-aware prompt.

        Parameters:
        -----------
        topic : str
            The main topic for the help query
        context : str
            Additional context about the current application state
        additional_info : str
            Any additional information to include in the prompt
        button_text : str
            Text to display on the button
        """
        # Get comprehensive context
        comprehensive_context = ChatGPTHelper._get_comprehensive_context()

        # Create the base prompt
        base_prompt = f"""I'm using the MELK Chemo Copilot, a chemometric analysis tool for spectroscopic data analysis using PLS regression.

I need help with: {topic}

Context: I'm currently working on {context}

Current Application State:
{comprehensive_context}

{additional_info}

Please provide a clear, practical explanation suitable for someone working with chemometric data analysis."""

        # URL encode the prompt
        encoded_prompt = urllib.parse.quote(base_prompt)

        # Create ChatGPT URL
        chatgpt_url = f"https://chat.openai.com/?q={encoded_prompt}"

        # Create help button
        if st.button(button_text, key=f"help_{hash(topic)}"):
            st.markdown(f'<meta http-equiv="refresh" content="0; url={chatgpt_url}">',
                       unsafe_allow_html=True)
            st.info("Opening ChatGPT in a new tab...")

    @staticmethod
    def create_contextual_help(step_name: str, section: str, specific_topic: str = "",
                             data_info: str = "") -> None:
        """
        Create contextual help for specific sections of the application.

        Parameters:
        -----------
        step_name : str
            Current step name (e.g., "Data Upload", "Preprocessing")
        section : str
            Current section within the step
        specific_topic : str
            Specific topic or issue
        data_info : str
            Information about the current data state
        """
        context = f"Step: {step_name}, Section: {section}"
        if data_info:
            context += f", Data: {data_info}"

        topic = specific_topic if specific_topic else f"{section} in {step_name}"

        ChatGPTHelper.create_help_icon(topic, context)

    @staticmethod
    def get_preprocessing_help_context(method_name: str, data_shape: tuple = None) -> str:
        """Get context-specific help for preprocessing methods."""
        context = f"preprocessing method selection and application, specifically about {method_name}"

        additional_info = f"""
Preprocessing method: {method_name}

Please explain:
1. What this preprocessing method does
2. When it should be used
3. Advantages and disadvantages
4. How it affects spectroscopic data
5. Best practices for this method in chemometric analysis
"""

        if data_shape:
            additional_info += f"\nMy data has {data_shape[0]} samples and {data_shape[1]} variables."

        return context, additional_info

    @staticmethod
    def get_optimization_help_context(results_df=None) -> str:
        """Get context-specific help for preprocessing optimization."""
        context = "automatic preprocessing optimization using cross-validation"

        additional_info = """
I'm running automatic preprocessing optimization to find the best method for my data.

Please explain:
1. How preprocessing optimization works in chemometrics
2. What RMSECV and R² metrics mean
3. How to interpret optimization results
4. How to choose the best preprocessing method
5. Common issues and troubleshooting tips
"""

        if results_df is not None:
            additional_info += f"\nI have optimization results with {len(results_df)} different method combinations."

        return context, additional_info

    @staticmethod
    def get_data_upload_help_context(file_type: str = "", error_msg: str = "") -> str:
        """Get context-specific help for data upload issues."""
        context = "data upload and file format requirements"

        additional_info = f"""
File type: {file_type}

Please explain:
1. Proper data format for chemometric analysis
2. How to structure spectroscopic data files
3. Common file format issues and solutions
4. Data quality requirements
5. Sample vs variable organization
"""

        if error_msg:
            additional_info += f"\nI'm getting this error: {error_msg}"

        return context, additional_info

    @staticmethod
    def get_visualization_help_context(plot_type: str, data_info: str = "") -> str:
        """Get context-specific help for data visualization."""
        context = f"data visualization and interpretation, specifically {plot_type}"

        additional_info = f"""
Plot type: {plot_type}
{data_info}

Please explain:
1. How to interpret this type of plot
2. What patterns to look for
3. Common issues and what they mean
4. Best practices for this visualization
5. How this relates to chemometric analysis
"""

        return context, additional_info
